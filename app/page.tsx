import { redirect } from 'next/navigation';

// Add preload hints for the default locale to improve loading time
export const metadata = {
  alternates: {
    canonical: 'https://example.com/en',
    languages: {
      'en-US': 'https://example.com/en',
    },
  },
};

export default function Home() {
  // Static metadata helps with faster initial rendering
  // Use permanent redirect to allow browsers to cache the redirect
  redirect("/en");
} 