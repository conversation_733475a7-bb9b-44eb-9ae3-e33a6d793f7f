import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { ThemeProvider } from '@/providers/ThemeProvider'
import { CookieConsent } from '@/components/ui/CookieConsent'
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'

// Optimize font loading with display: swap
const inter = Inter({ 
  subsets: ['latin', 'cyrillic'],
  display: 'swap',
  preload: true,
  fallback: ['system-ui', 'sans-serif']
})

export const metadata: Metadata = {
  title: 'Company Name - Digital Solutions',
  description: 'We develop cutting-edge mobile and web applications that transform businesses through innovative technology.',
  keywords: 'digital solutions, web development, mobile apps, technology innovation, business transformation, software development',
  metadataBase: new URL('https://example.com'),
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-video-preview': -1,
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'Company Name - Digital Solutions',
    description: 'We develop cutting-edge mobile and web applications that transform businesses through innovative technology.',
    url: 'https://example.com',
    siteName: 'Company Name',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Company Name - Digital Solutions',
    description: 'We develop cutting-edge mobile and web applications that transform businesses through innovative technology.',
  },
  verification: {
    google: 'google-site-verification-code', // Replace with your actual verification code when you have it
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="h-full" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
      </head>
      <body
        className={`${inter.className} h-full bg-white dark:bg-gray-950 antialiased`}
      >
        {/* Skip Link für bessere Barrierefreiheit - direkt zum Hauptinhalt springen */}
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-white dark:bg-gray-800 text-primary dark:text-[#8fa3c7] px-4 py-2 rounded-md shadow-md focus:outline-none"
          tabIndex={0}
        >
          Zum Hauptinhalt springen
        </a>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
          {children}
          {/* Defer non-critical components */}
          <CookieConsent />
          <Analytics />
          <SpeedInsights />
        </ThemeProvider>
      </body>
    </html>
  );
}
