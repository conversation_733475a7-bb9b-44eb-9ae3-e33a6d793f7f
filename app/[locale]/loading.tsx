import React from "react";

export default function Loading() {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-white dark:bg-gray-950">
      <div className="flex flex-col items-center space-y-4">
        <div className="relative w-16 h-16">
          {/* Simple spinner with hardware-accelerated animation */}
          <div className="absolute inset-0 rounded-full border-4 border-gray-200 dark:border-gray-700"></div>
          <div
            className="absolute inset-0 rounded-full border-4 border-t-primary animate-spin"
            style={{
              animationDuration: "0.75s",
              willChange: "transform",
            }}
          ></div>
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400 animate-pulse">
          Loading...
        </p>
      </div>
    </div>
  );
}
