import React from 'react'

type OrganizationProps = {
  name: string
  url: string
  logo: string
  sameAs: string[]
  address: {
    streetAddress: string
    addressLocality: string
    addressRegion: string
    postalCode: string
    addressCountry: string
  }
}

export default function OrganizationJsonLd({
  name = "Your Business",
  url = "https://yourbusiness.com",
  logo = "https://yourbusiness.com/logo.png",
  sameAs = [
    "https://facebook.com/yourbusiness",
    "https://twitter.com/yourbusiness",
    "https://instagram.com/yourbusiness",
    "https://linkedin.com/company/yourbusiness",
  ],
  address = {
    streetAddress: "123 Business Avenue",
    addressLocality: "New York",
    addressRegion: "NY",
    postalCode: "10001",
    addressCountry: "US",
  },
}: Partial<OrganizationProps>) {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name,
    url,
    logo,
    sameAs,
    address: {
      "@type": "PostalAddress",
      ...address,
    },
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "******-123-4567",
      contactType: "customer service",
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  );
}
