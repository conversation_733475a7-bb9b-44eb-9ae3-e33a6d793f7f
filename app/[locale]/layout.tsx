import type { <PERSON>ada<PERSON> } from 'next'
import { locales } from '@/middleware'
import { I18nProvider } from '@/providers/I18nProvider'
import { Header } from '@/components/ui/Header'
import OrganizationJsonLd from "./JsonLd";
import { Suspense } from "react";

// Define dynamic metadata for locale-specific SEO
export async function generateMetadata({
  params,
}: {
  params: { locale: string };
}) {
  // Await params before destructuring to avoid Next.js warning
  const { locale } = await Promise.resolve(params);

  return {
    title: {
      default: "Your Business - Professional Solutions",
      template: "%s | Your Business",
    },
    description:
      "We provide professional solutions tailored to your unique business needs.",
    alternates: {
      canonical: `https://yourbusiness.com/${locale}`,
      languages: {
        en: "https://yourbusiness.com/en",
        ar: "https://yourbusiness.com/ar",
        // Add other languages as needed
      },
    },
    openGraph: {
      title: "Your Business - Professional Solutions",
      description:
        "We provide professional solutions tailored to your unique business needs.",
      url: `https://yourbusiness.com/${locale}`,
      siteName: "Your Business",
      locale: locale === "ar" ? "ar_SA" : "en_US",
      type: "website",
    },
  };
}

// Generate static params for all locales
export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

// Loading placeholder to avoid blank screen
function LoadingPlaceholder() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-white dark:bg-gray-950">
      <div className="animate-pulse space-y-4">
        <div className="h-8 w-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="h-32 w-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    </div>
  );
}

export default function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: any;
}) {
  return (
    <I18nProvider>
      <OrganizationJsonLd />
      <Header />
      <Suspense fallback={<LoadingPlaceholder />}>
        <div id="main-content">{children}</div>
      </Suspense>
    </I18nProvider>
  );
}
