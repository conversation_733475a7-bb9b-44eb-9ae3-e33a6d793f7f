import { HeroSection } from '@/components/sections/HeroSection'
import { AboutSection } from "@/components/sections/AboutSection";
import { TestimonialsSection } from '@/components/sections/TestimonialsSection'
import BookingCalendar from "@/components/booking/BookingCalendar";
import { ContactSection } from "@/components/sections/ContactSection";
import { FooterSection } from "@/components/sections/FooterSection";
import { getDictionary } from "@/lib/dictionaries";
import { ServiceSection } from "@/components/sections/ServiceSection";
import { FloatingContact } from "@/components/ui/FloatingContact";
import { FloatingCTABanner } from "@/components/ui/FloatingCTABanner";
import { Suspense, lazy } from "react";

// Fix for ServiceSection type error
const ServiceSectionWithProps = (props: any) => <ServiceSection {...props} />;

// Lazy load non-critical components
const LazyTestimonialsSection = lazy(() =>
  import("@/components/sections/TestimonialsSection").then((mod) => ({
    default: mod.TestimonialsSection,
  }))
);
const LazyBookingCalendar = lazy(() =>
  import("@/components/booking/BookingCalendar").then((mod) => ({
    default: mod.default,
  }))
);

// Simple loading placeholders
function SectionPlaceholder() {
  return (
    <div className="w-full h-64 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-lg"></div>
  );
}

interface HomeProps {
  params: Promise<{ locale: string }>;
}

export default async function Home({ params }: HomeProps) {
  const { locale } = await params;
  // Get the dictionary for the current locale
  const dict = await getDictionary(locale);

  return (
    <main
      className={`flex min-h-screen flex-col items-center w-full overflow-x-hidden justify-between ${
        locale === "ar" ? "rtl" : ""
      }`}
    >
      {/* Floating Contact Component */}
      <FloatingContact dictionary={dict.contact} />

      {/* Floating CTA Banner */}
      <FloatingCTABanner dictionary={dict.cta} />

      {/* Critical sections loaded immediately */}
      {/* Hero Section - Introduction and company overview */}
      <HeroSection dictionary={dict.hero} />

      {/* Service Section - Our services offerings */}
      <ServiceSectionWithProps dictionary={dict} />

      {/* About Section - Vision, Mission, and Founder with integrated Clients */}
      <AboutSection dictionary={dict} clientsDictionary={dict.clients} />

      {/* Non-critical sections loaded progressively */}
      {/* Testimonials Section - Client testimonials */}
      <Suspense fallback={<SectionPlaceholder />}>
        <LazyTestimonialsSection dictionary={dict} />
      </Suspense>

      {/* Booking Calendar - Schedule appointments */}
      <Suspense fallback={<SectionPlaceholder />}>
        <LazyBookingCalendar dictionary={dict.booking} />
      </Suspense>

      {/* Contact Section - Get in touch */}
      <ContactSection dictionary={dict.contact} />

      {/* Footer Section - Links and copyright */}
      <FooterSection dictionary={dict.footer} />
    </main>
  );
}