# Homepage Template Blueprint

Dieses Dokument bietet einen umfassenden Überblick über die Struktur der Homepage-Vorlage, die für verschiedene Geschäftsbereiche angepasst werden kann (z.B. Anwälte, Ärzte, Restaurants).

## Projektstruktur

```
homepage_template/
├── app/                       # Next.js App Router
│   ├── [locale]/              # Sprachbasiertes Routing
│   │   ├── page.tsx           # Haupt-Homepage-Komponente
│   │   └── layout.tsx         # Layout für lokalisierte Routen
│   ├── booking/               # Buchungsseite
├── src/
│   ├── components/            # UI-Komponenten
│   │   ├── sections/          # Hauptseitenabschnitte
│   │   ├── ui/                # Wiederverwendbare UI-Komponenten
│   │   ├── pricing/           # Preiskalkulationskomponenten
│   │   ├── booking/           # Buchungskalender-Komponenten
│   │   └── layout/            # Layout-Komponenten
│   ├── dictionaries/          # Mehrsprachige Inhalte
│   ├── lib/                   # Hilfsfunktionen
│   ├── providers/             # React-Kontextanbieter
│   └── types/                 # TypeScript-Typdefinitionen
├── public/                    # Statische Assets
│   └── images/                # Bilder und Medien
```

## Schlüsselkomponenten

Die Homepage besteht aus den folgenden Hauptabschnitten, die alle anpassbar sind:

1. **FloatingContact** - Schwebende Kontaktschaltfläche für schnellen Zugriff
2. **HeroSection** - Hauptbanner mit Einführung
3. **SolutionsPortfolioSection** - Präsentation von Lösungen und Portfolio
4. **ServiceSection** - Angebotene Dienstleistungen
5. **AboutSection** - Unternehmensinformationen (Vision, Mission, Team)
6. **TestimonialsSection** - Kundenbewertungen
8. **PricingCalculator** - Interaktives Preisberechnungstool
9. **ContactSection** - Kontaktinformationen und Formular
10. **FooterSection** - Fußzeile mit Links und Copyright
11. **BookingCalendar** - Interaktiver Buchungskalender für Termine

## Anpassungspunkte für verschiedene Geschäftsbereiche

### 1. Inhaltsanpassung

Die Hauptinhalte werden über Wörterbücher in `src/dictionaries/` verwaltet, was eine einfache Textanpassung für verschiedene Geschäftsbereiche ermöglicht. Die Platzhalter wurden entfernt und durch direkte Inhalte ersetzt, um die Anpassung zu vereinfachen.

### 2. Visuelle Assets

Ersetzen Sie Bilder im Verzeichnis `public/images/` durch branchenspezifische Visuals:
- Professionelle Porträts für Anwälte/Ärzte
- Essensfotos für Restaurants
- Branchenspezifische Ikonographie

### 3. Dienstleistungsangebote

Passen Sie die Komponente `ServiceSection.tsx` an, um Dienstleistungen darzustellen, die für den Geschäftsbereich spezifisch sind:
- Rechtsdienstleistungen für Anwälte
- Medizinische Fachgebiete für Ärzte
- Menükategorien für Restaurants

### 4. Portfolio-Beispiele

Aktualisieren Sie das Products-Array in `page.tsx` mit branchenrelevanten Beispielen:
- Fallstudien für Anwälte
- Erfolgsgeschichten von Patienten für Ärzte (anonymisiert)
- Spezialgerichte für Restaurants

### 5. Preisstruktur

Passen Sie die Komponente `PricingCalculator` an, um das Preismodell des Unternehmens widerzuspiegeln:
- Stundensätze oder Pauschalgebühren für Anwälte
- Beratungsgebühren für Ärzte
- Menüpreise für Restaurants

### 6. Buchungssystem

Die neue `BookingCalendar`-Komponente bietet ein interaktives Terminbuchungssystem:
- Anpassbare Zeitslots und Verfügbarkeiten
- Responsives Design mit vertikalen Zeitauswahlpfeilen
- Nahtlose Integration in die Geschäftswebsite

## Anpassungsrichtlinien

### Für Anwaltskanzleien
- Betonen Sie Vertrauen, Fachwissen und Erfolgsgeschichte
- Konzentrieren Sie sich auf Fachgebiete in der ServiceSection
- Fügen Sie Fallergebnisse im Portfolio hinzu (unter Wahrung der Mandantenvertraulichkeit)
- Ergänzen Sie Anwaltsprofile in der AboutSection
- Passen Sie FAQ für rechtliche Belange an
- Integrieren Sie den Buchungskalender für Beratungstermine

### Für Arztpraxen
- Heben Sie Patientenversorgung und medizinisches Fachwissen hervor
- Organisieren Sie Dienstleistungen nach Fachgebieten oder Behandlungen
- Fügen Sie Arztqualifikationen und Spezialisierungen hinzu
- Ergänzen Sie Versicherungsinformationen
- Betonen Sie Patientenbewertungen (mit entsprechender Einwilligung)
- Nutzen Sie den Buchungskalender für Patiententermine

### Für Restaurants
- Präsentieren Sie Essensfotos prominent im Hero-Bereich
- Ersetzen Sie ServiceSection durch Menükategorien
- Integrieren Sie ein Reservierungssystem mit dem Buchungskalender
- Fügen Sie Geschäftszeiten gut sichtbar ein
- Ergänzen Sie eine Galerie der Restaurantatmosphäre und Gerichte

## Technische Implementierungshinweise

1. Inhaltliche Änderungen erfolgen hauptsächlich durch Aktualisierung der Wörterbuchdateien in `src/dictionaries/`
2. Visuelle Änderungen erfordern die Aktualisierung von Bildern in `public/images/`
3. Strukturelle Änderungen können Modifikationen an Komponenten in `src/components/sections/` erfordern
4. Für wesentliche Layout-Änderungen, ändern Sie die Hauptseitenstruktur in `app/[locale]/page.tsx`
5. Der Buchungskalender kann in `src/components/booking/BookingCalendar.tsx` angepasst werden

## Priorität bei der Komponentenanpassung

Bei der Anpassung für einen neuen Geschäftsbereich, ändern Sie die Komponenten in dieser Reihenfolge:

1. Wörterbuchinhalte (am einfachsten, keine Codeänderungen)
2. Bilder und visuelle Assets
3. Struktur der Dienstleistungsangebote
4. Preismodell
5. Buchungskalender-Anpassungen
6. Seitenstruktur (falls erforderlich)

Dieser Ansatz gewährleistet den effizientesten Anpassungsprozess unter Beibehaltung der Kernfunktionalität und des responsiven Designs der Vorlage.

## Wichtige Änderungen in der aktuellen Version

1. **Entfernung von Platzhaltern**: Die Template-Platzhalter wie `{{companyName}}` wurden entfernt und durch direkte Inhalte ersetzt
2. **Verbesserter Buchungskalender**: 
   - Vertikale Pfeile für die Stundenauswahl
   - Vereinfachte Minutenauswahl mit einem Toggle zwischen 00 und 30
   - Subtiler Kalenderhintergrund für bessere visuelle Orientierung
   - Vergrößerte Schrift für Tage und Datum
3. **Optimierte Abschnitte**:
   - Entfernung des Farbverlaufs in der AboutSection
   - Konsistente Titelüberschriften in allen Abschnitten
   - Verbesserte Kontaktbanner-Darstellung
