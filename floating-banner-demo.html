<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Floating CTA Banner Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Floating CTA Banner Animations */
        @keyframes scroll-x {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
        }
        
        .animate-scroll-x {
            animation: scroll-x 20s linear infinite;
        }
        
        .animate-scroll-x:hover {
            animation-play-state: paused;
        }

        body {
            height: 200vh; /* Make page scrollable for demo */
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Demo Content to show scrolling effect -->
    <div class="max-w-4xl mx-auto p-8">
        <h1 class="text-4xl font-bold text-gray-900 mb-8">Lawyer Template Demo</h1>
        <p class="text-lg text-gray-600 mb-8">Scroll down to see the floating CTA banner appear...</p>
        
        <div class="space-y-8">
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-semibold mb-4">Legal Services</h2>
                <p class="text-gray-600">Expert legal advice for individuals and businesses...</p>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-semibold mb-4">Our Team</h2>
                <p class="text-gray-600">Experienced professionals dedicated to your success...</p>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-semibold mb-4">Contact Us</h2>
                <p class="text-gray-600">Get in touch for a free consultation...</p>
            </div>
        </div>
    </div>

    <!-- Floating Contact Buttons (to show positioning) -->
    <div class="fixed bottom-4 right-4 z-50">
        <div class="flex flex-col gap-2">
            <button class="w-12 h-12 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-colors">
                📞
            </button>
            <button class="w-12 h-12 bg-green-600 text-white rounded-full shadow-lg hover:bg-green-700 transition-colors">
                💬
            </button>
        </div>
    </div>

    <!-- Floating CTA Banner -->
    <div id="floating-banner" class="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-40 w-[90%] max-w-6xl opacity-0 translate-y-20 transition-all duration-500" style="margin-right: 120px;">
        <div class="bg-white/95 backdrop-blur-lg border border-gray-200/50 rounded-2xl shadow-2xl p-4">
            <div class="flex items-center justify-between gap-4">
                
                <!-- Left Side - Premium Quality Badge -->
                <div class="flex-shrink-0">
                    <div class="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-emerald-600 to-blue-600 text-white rounded-xl shadow-lg">
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-yellow-300" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        </div>
                        <span class="text-sm font-bold whitespace-nowrap">Premium Quality</span>
                    </div>
                </div>

                <!-- Middle - Scrolling Features -->
                <div class="flex-1 overflow-hidden mx-4">
                    <div class="relative">
                        <div class="flex animate-scroll-x gap-6">
                            <!-- First set of features -->
                            <div class="flex items-center gap-2 whitespace-nowrap px-3 py-1 bg-gray-50 rounded-lg">
                                <svg class="w-4 h-4 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-gray-700">SEO-optimized</span>
                            </div>
                            
                            <div class="flex items-center gap-2 whitespace-nowrap px-3 py-1 bg-gray-50 rounded-lg">
                                <svg class="w-4 h-4 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-gray-700">5-language support (EN, DE, TR, RU, AR)</span>
                            </div>
                            
                            <div class="flex items-center gap-2 whitespace-nowrap px-3 py-1 bg-gray-50 rounded-lg">
                                <svg class="w-4 h-4 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-gray-700">Dark/light theme</span>
                            </div>
                            
                            <div class="flex items-center gap-2 whitespace-nowrap px-3 py-1 bg-gray-50 rounded-lg">
                                <svg class="w-4 h-4 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-gray-700">Notion & other integrations</span>
                            </div>
                            
                            <div class="flex items-center gap-2 whitespace-nowrap px-3 py-1 bg-gray-50 rounded-lg">
                                <svg class="w-4 h-4 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-gray-700">Delivered in 7–10 business days</span>
                            </div>
                            
                            <div class="flex items-center gap-2 whitespace-nowrap px-3 py-1 bg-gray-50 rounded-lg">
                                <svg class="w-4 h-4 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-gray-700">1-on-1 developer support</span>
                            </div>
                            
                            <!-- Duplicate set for seamless loop -->
                            <div class="flex items-center gap-2 whitespace-nowrap px-3 py-1 bg-gray-50 rounded-lg">
                                <svg class="w-4 h-4 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-gray-700">SEO-optimized</span>
                            </div>
                            
                            <div class="flex items-center gap-2 whitespace-nowrap px-3 py-1 bg-gray-50 rounded-lg">
                                <svg class="w-4 h-4 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-gray-700">5-language support (EN, DE, TR, RU, AR)</span>
                            </div>
                            
                            <div class="flex items-center gap-2 whitespace-nowrap px-3 py-1 bg-gray-50 rounded-lg">
                                <svg class="w-4 h-4 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium text-gray-700">Dark/light theme</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side - CTA Buttons -->
                <div class="flex-shrink-0 flex gap-2">
                    <a href="https://www.digify-now.com#templates" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       class="group inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-sm font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                        <span class="hidden sm:inline">See all templates</span>
                        <span class="sm:hidden">Templates</span>
                        <svg class="w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                    </a>

                    <a href="https://www.digify-now.com#contact" 
                       target="_blank" 
                       rel="noopener noreferrer"
                       class="group inline-flex items-center gap-2 px-4 py-2 bg-white text-gray-900 text-sm font-semibold rounded-lg border border-gray-200 hover:border-blue-300 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                        <span class="hidden sm:inline">Book free consultation</span>
                        <span class="sm:hidden">Consult</span>
                        <svg class="w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Show banner after scrolling down
        window.addEventListener('scroll', () => {
            const banner = document.getElementById('floating-banner');
            if (window.scrollY > 300) {
                banner.style.opacity = '1';
                banner.style.transform = 'translateX(-50%) translateY(0)';
            } else {
                banner.style.opacity = '0';
                banner.style.transform = 'translateX(-50%) translateY(80px)';
            }
        });
    </script>
</body>
</html> 