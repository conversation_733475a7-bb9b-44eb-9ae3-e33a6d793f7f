/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Disable ESLint during build for deployment
    ignoreDuringBuilds: true,
  },
  images: {
    domains: ["images.unsplash.com"],
    // Enable image optimization instead of unoptimized
    unoptimized: false,
    // Add quality settings for image optimization
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    formats: ["image/webp", "image/avif"],
  },
  // For TypeScript error handling
  typescript: {
    // Disable TypeScript errors during build
    ignoreBuildErrors: true,
  },
  // Enable React strict mode for better performance and fewer bugs
  reactStrictMode: true,
  // Improve page loading speed with server components
  compiler: {
    removeConsole: process.env.NODE_ENV === "production",
  },
  // Enable progressive web app features
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
  },
  // Configure output
  output: "standalone",
  // Configure image loader with quality
  swcMinify: true,
};

module.exports = nextConfig 