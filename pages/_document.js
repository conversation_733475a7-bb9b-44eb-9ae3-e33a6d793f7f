import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#1E40AF" />
        <link rel="icon" href="/favicon.ico" />
        {/* Open Graph tags */}
        <meta property="og:type" content="website" />
        <meta
          property="og:title"
          content="Company Name - Digital Solutions"
        />
        <meta
          property="og:description"
          content="Leading digital solutions provider specializing in Flutter mobile apps, Next.js websites and AI integrations."
        />
        <meta property="og:url" content="https://example.com" />
        <meta
          property="og:image"
          content="https://example.com/og-image.png"
        />
        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:site" content="@companyname" />
        <meta
          name="twitter:title"
          content="Company Name - Digital Solutions"
        />
        <meta
          name="twitter:description"
          content="Leading digital solutions provider specializing in Flutter mobile apps, Next.js websites and AI integrations."
        />
        <meta
          name="twitter:image"
          content="https://example.com/twitter-image.png"
        />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
} 