# Neutral Homepage Template

A versatile, industry-agnostic Next.js homepage template that can be easily customized for various business types. This template features a modern design with full multilingual support, dark/light mode, and accessibility compliance.

## Features

- 🌐 **Multi-language Support**: Built-in support for English, German, Turkish, and Arabic
- 🎨 **Dark/Light Mode**: Fully supports theme switching
- ♿ **Accessibility**: WCAG 2.2 compliant design
- 📱 **Responsive Design**: Looks great on all devices
- 🔌 **Modular Components**: Easy-to-customize section components
- 🚀 **App Router**: Uses Next.js App Router architecture

## Getting Started

### Installation

```bash
# Install dependencies
npm install

# Run the development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the site in your browser.

## Customization Guide

This template has been neutralized to work for any industry. Follow these steps to customize it for your specific needs:

### 1. Replace Company Information

- Update the company name in `src/components/sections/FooterSection.tsx`
- Replace contact information (email, phone, social links) in `src/components/sections/FooterSection.tsx`
- Update the favicon and other brand assets in the `public` directory

### 2. Customize Content

- Edit the dictionary files in `src/dictionaries/` to match your industry terminology:
  - `en.json`: English content
  - `de.json`: German content
  - `tr.json`: Turkish content
  - `ar.json`: Arabic content

### 3. Replace Images

- Replace placeholder images in `public/images/` with your own branded visuals
- Key directories to update:
  - `public/images/mockups/placeholders/`: Project showcase images
  - `public/images/testimonials/`: Client testimonial photos
  - `public/images/companies/placeholders/`: Client/partner logos

### 4. Customize Sections

The homepage consists of these main sections that you can customize:

- **HeroSection**: Main banner/intro area
- **ServiceSection**: Your service offerings
- **SolutionsPortfolioSection**: Showcase of your work
- **AboutSection**: Company story and team
- **TestimonialsSection**: Client feedback
- **FaqSection**: Common questions
- **PricingCalculator**: Interactive pricing tool
- **ContactSection**: Contact form and details
- **FooterSection**: Footer links and info

## Multilingual Support

The template supports multiple languages through the dictionary files. To add a new language:

1. Create a new dictionary file in `src/dictionaries/` (e.g., `fr.json` for French)
2. Copy the structure from an existing file like `en.json`
3. Translate all content
4. Update the language selector in the navigation component

## Theming

The template uses Tailwind CSS for styling with a theme system that supports both light and dark modes.

- Edit global styles in `app/globals.css`
- Theme configuration is in `tailwind.config.js`

## Tech Stack

- [Next.js](https://nextjs.org/) - React framework
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [Framer Motion](https://www.framer.com/motion/) - Animations

## Deployment

This template can be deployed to any hosting platform that supports Next.js, such as Vercel, Netlify, or a traditional server.

```bash
# Build for production
npm run build

# Start production server
npm start
```

## License

This template is available for personal and commercial use.

---

Created with ❤️ for easy customization across multiple industries.
