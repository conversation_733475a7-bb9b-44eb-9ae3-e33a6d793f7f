{"name": "website_nextjs_react", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@formatjs/intl-localematcher": "^0.6.1", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@number-flow/react": "^0.5.9", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-toggle": "^1.1.2", "@tabler/icons-react": "^3.31.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@types/nodemailer": "^6.4.17", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "embla-carousel": "^8.6.0", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.7.4", "lottie-react": "^2.4.1", "lucide-react": "^0.484.0", "motion": "^12.6.3", "negotiator": "^1.0.0", "next": "^15.3.2", "next-intl": "^4.0.2", "next-themes": "^0.4.6", "nodemailer": "^6.10.0", "react": "^19.0.0", "react-countup": "^6.5.3", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-type-animation": "^3.2.0", "sharp": "^0.33.5", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/postcss": "^4", "@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "eslint": "^9", "eslint-config-next": "15.2.1", "tailwindcss": "^4", "typescript": "^5"}}