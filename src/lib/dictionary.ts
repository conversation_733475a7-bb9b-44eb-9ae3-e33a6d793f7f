import 'server-only'

// Define Locale type directly here to avoid import issues
export type Locale = 'en' | 'de' | 'ru' | 'tr' | 'ar'

const dictionaries = {
  en: () => import('@/dictionaries/en.json').then((module) => module.default),
  de: () => import('@/dictionaries/de.json').then((module) => module.default),
  ru: () => import('@/dictionaries/ru.json').then((module) => module.default),
  tr: () => import('@/dictionaries/tr.json').then((module) => module.default),
  ar: () => import('@/dictionaries/ar.json').then((module) => module.default),
}

export interface Dictionary {
  hero: {
    title: string;
    subtitle: string;
    description: string;
    ctaPrimary?: string;
    bookConsultation?: string;
    cta?: {
      primary: string;
      secondary: string;
      calculator?: string;
    };
    typing: {
      businessGrowth: string;
      digitalSolution: string;
      userExperience: string;
      technologyInnovation: string;
    };
    solutions: {
      streamlinedOperations: {
        title: string;
        description: string;
      };
      enhancedUserExperience: {
        title: string;
        description: string;
      };
      dataInsights: {
        title: string;
        description: string;
      };
      scalableArchitecture: {
        title: string;
        description: string;
      };
    };
  };
  prices: {
    title: string;
    subtitle: string;
    description?: string;
    metaTitle?: string;
    metaDescription?: string;
    caseStudyTitle?: string;
    caseStudyDescription?: string;
    promotionTitle?: string;
    promotionDescription?: string;
    calculatorTitle?: string;
    calculatorDescription?: string;
    calculatorButton?: string;
    discussButton?: string;
    contactButton?: string;
    packages?: {
      mvp?: {
        title: string;
        timeframe: string;
        description: string;
        features: string[];
      };
      prototype?: {
        title: string;
        timeframe: string;
        description: string;
        features: string[];
      };
      architecture?: {
        title: string;
        timeframe: string;
        description: string;
        features: string[];
      };
      consulting?: {
        title: string;
        timeframe: string;
        description: string;
        features: string[];
      };
    };
  };
  solutionsPortfolio: {
    description: any;
    title: string;
    subtitle: string;
    clickInstruction: string;
    imageCounter: string; // Added for "Image {current} of {total}"
    keyFeatures: string;
    problemsSolved: string;
    categories: {
      all: string;
      aiAssistant: string;
      foodDelivery: string;
      hospitality: string;
      medical: string;
      lifestyle: string;
      automotive: string;
    };
    backButton?: string; // Added
    relatedProjects?: string; // Added
    noRelatedProjects?: string; // Added
    clickToExpand?: string; // Added
    noImagesAvailable?: string; // Added
    items: {
      spotzAiAssistant: {
        title: string;
        description: string;
        imageAlt: string;
      };
      foodDelivery: {
        title: string;
        description: string;
        imageAlt: string;
      };
      hostIQ: {
        title: string;
        description: string;
        imageAlt: string;
      };
      medicalApp: {
        title: string;
        description: string;
        imageAlt: string;
      };
      lifestyleApp: {
        title: string;
        description: string;
        imageAlt: string;
      };
      nearby: {
        title: string;
        description: string;
        imageAlt: string;
      };
      toggCarControl: {
        title: string;
        description: string;
        imageAlt: string;
      };
      lifestylePlatform: {
        title: string;
        description: string;
        imageAlt: string;
      };
      // Add optional default for fallback
      default?: {
        imageAlt: string;
      };
    };
    solutions: {
      aiAssistant: {
        title: string;
        description: string;
        features: string[];
        problemsSolved: string[];
      };
      foodDelivery: {
        title: string;
        description: string;
        features: string[];
        problemsSolved: string[];
      };
      hospitality: {
        title: string;
        description: string;
        features: string[];
        problemsSolved: string[];
      };
      medical: {
        title: string;
        description: string;
        features: string[];
        problemsSolved: string[];
      };
      lifestyle: {
        title: string;
        description: string;
        features: string[];
        problemsSolved: string[];
      };
      automotive: {
        title: string;
        description: string;
        features: string[];
        problemsSolved: string[];
      };
    };
  };
  about: {
    title: string;
    statsTitle?: string;
    statsDesc?: string;
    featuredPropertiesTitle?: string;
    featuredPropertiesDesc?: string;
    subtitle: string;
    description?: string;
    vision: string;
    visionDesc: string;
    mission: string;
    missionDesc: string;
    founderTitle: string;
    founderDesc: string;
    skills: string;
    projects: string;
    testimonials: string;
    experience: string;
    clients: string;
    transformBusiness: string;
    createSolutions: string;
    stayingAhead: string;
    exceptionalUX: string;
    highPerformance: string;
    solvingChallenges: string;
    flutterExpert: string;
    webDevAdvanced: string;
    aiIntegration: string;
    successRate?: string;
    casesWon?: string;
    publications?: string;
    meetTeam?: string;
    values?: {
      clientOrientation?: {
        title?: string;
        description?: string;
      };
      expertise?: {
        title?: string;
        description?: string;
      };
      integrity?: {
        title?: string;
        description?: string;
      };
      commitment?: {
        title?: string;
        description?: string;
      };
    };
    team?: {
      lawyer1?: {
        name?: string;
        role?: string;
        bio?: string;
        expertise?: {
          divorce?: string;
          custody?: string;
          maintenance?: string;
          assets?: string;
        }
      };
      lawyer2?: {
        name?: string;
        role?: string;
        bio?: string;
        expertise?: {
          dismissal?: string;
          contracts?: string;
          severance?: string;
          workCouncil?: string;
        }
      };
      lawyer3?: {
        name?: string;
        role?: string;
        bio?: string;
        expertise?: {
          economic?: string;
          defense?: string;
          juvenile?: string;
        }
      };
      lawyer4?: {
        name?: string;
        role?: string;
        bio?: string;
        expertise?: {
          accident?: string;
          fines?: string;
          license?: string;
        }
      };
      lawyer5?: {
        name?: string;
        role?: string;
        bio?: string;
        expertise?: {
          contractDesign?: string;
          commercialLaw?: string;
          termsConditions?: string;
        }
      };
      lawyer6?: {
        name?: string;
        role?: string;
        bio?: string;
        expertise?: {
          willCreation?: string;
          inheritance?: string;
          compulsory?: string;
        }
      };
    };
    expertise?: {
      familyLaw?: {
        title?: string;
        description?: string;
      };
      employmentLaw?: {
        title?: string;
        description?: string;
      };
      criminalLaw?: {
        title?: string;
        description?: string;
      };
      trafficLaw?: {
        title?: string;
        description?: string;
      };
    };
  };
  advantages: {
    title: string;
    subtitle: string;
    speed: string;
    speedDesc: string;
    stability: string;
    stabilityDesc: string;
    cost: string;
    costDesc: string;
    timeToMarket: string;
    timeToMarketDesc: string;
    aiIntegration: string;
    aiIntegrationDesc: string;
    development: string;
    developmentTime: string;
    developmentDesc: string;
    developmentEfficiency: string;
    mvp: string;
    mvpTime: string;
    mvpDesc: string;
    timeToMarketReduction: string;
    prototype: string;
    prototypeTime: string;
    prototypeDesc: string;
    conceptValidation: string;
    qa: string;
    qaTime: string;
    qaDesc: string;
    bugFreeRate: string;
    consulting: string;
    consultingTime: string;
    consultingDesc: string;
    technicalImprovement: string;
    homepage?: string;
    homepageTime?: string;
    homepageDesc?: string;
    developmentInfo: {
      title: string;
      simpleApp: {
        title: string;
        examples: string;
        features: string[];
        timeline: {
          total: string;
          frontend: string;
          backend: string;
          testing: string;
        };
      };
      mediumApp: {
        title: string;
        examples: string;
        features: string[];
        timeline: {
          total: string;
          frontend: string;
          backend: string;
          testing: string;
        };
      };
      complexApp: {
        title: string;
        examples: string;
        features: string[];
        timeline: {
          total: string;
          frontend: string;
          backend: string;
          testing: string;
        };
      };
      factors: {
        title: string;
        teamSize: string;
        technology: string;
        requirements: string;
        testing: string;
        design: string;
      };
      summary: string;
      aiComparison: string;
    };
  };
  serviceSection: {
    title: string;
    subtitle: string;
    description: string;
    viewAll: string;
    backToServices?: string;
    overview?: string;
    features?: string;
    mobileApps?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    webDev?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    uiuxDesign?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    consulting?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    aiSolutions?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    prototype?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    mvp?: {
      title: string;
      description: string;
      benefits?: string[];
    };
    fullstack?: {
      title: string;
      description: string;
      benefits?: string[];
    };
  };
  services: {
    title: string;
    subtitle: string;
    description: string;
    frontend: string;
    frontendDesc: string;
    backend: string;
    backendDesc: string;
    mobile: string;
    mobileDesc: string;
    ai: string;
    aiDesc: string;
    mobileApps: string;
    mobileAppsDesc: string;
    webDev: string;
    webDevDesc: string;
    uiuxDesign: string;
    uiuxDesignDesc: string;
    consulting: string;
    consultingDesc: string;
    aiSolutions: string;
    aiSolutionsDesc: string;
    viewAll: string;
  };
  packages: {
    title: string;
    subtitle: string;
    mvp: {
      title: string;
      timeframe: string;
      description: string;
      features: string[];
      price?: number; // Made optional
      marketPrice?: number; // Made optional
    };
    prototype: {
      title: string;
      timeframe: string;
      description: string;
      features: string[];
      price?: number; // Made optional
      marketPrice?: number; // Made optional
    };
    architecture: {
      title: string;
      timeframe: string;
      description: string;
      features: string[];
      price?: number; // Made optional
      marketPrice?: number; // Made optional
    };
    consulting: {
      title: string;
      timeframe: string;
      description: string;
      features: string[];
      price?: number; // Made optional
      marketPrice?: number; // Made optional
    };
  };
  portfolio: {
    title: string;
    subtitle: string;
    all: string;
    screenshot: string;
    screenshots: string;
    viewDetails: string;
    viewAllProjects: string;
    problemsWeSolve: string;
    noSectorsFound: string;
    categories: {
      aiAssistant: string;
      foodDelivery: string;
      hospitality: string;
      business: string;
      social: string;
      automotive: string;
    };
    sectors: {
      assistant: string;
      food: string;
      hospitality: string;
      lifestyle: string;
      social: string;
      automotive: string;
      medical: string;
      business: string;
    };
    sectorDescriptions: {
      assistant: string;
      food: string;
      hospitality: string;
      lifestyle: string;
      social: string;
      automotive: string;
      medical: string;
      business: string;
    };
    problems: {
      assistant: {
        1: string;
        2: string;
        3: string;
      };
      food: {
        1: string;
        2: string;
        3: string;
      };
      hospitality: {
        1: string;
        2: string;
        3: string;
      };
      lifestyle: {
        1: string;
        2: string;
        3: string;
      };
      social: {
        1: string;
        2: string;
        3: string;
      };
      automotive: {
        1: string;
        2: string;
        3: string;
      };
      medical: {
        1: string;
        2: string;
        3: string;
      };
      business: {
        1: string;
        2: string;
        3: string;
      };
    };
  };
  clients: {
    title: string;
    subtitle: string;
    visitWebsite: string;
  };
  testimonials: {
    title: string;
    subtitle: string;
    description?: string;
    readMore: string;
    readLess: string;
    formTitle: string;
    name?: string;
    email?: string;
    yourName?: string;
    yourEmail?: string;
    caseType?: string;
    rating?: string;
    stars?: string;
    yourExperience?: string;
    experienceDescription?: string;
    submitReview?: string;
    selectOption?: string;
    familyLaw?: string;
    employmentLaw?: string;
    criminalLaw?: string;
    trafficLaw?: string;
    contractLaw?: string;
  };
  contact: {
    title: string;
    subtitle: string;
    description?: string;
    name: string;
    email: string;
    phone: string;
    message: string;
    send: string;
    yourName: string;
    yourEmail: string;
    subject: string;
    howCanIHelp: string;
    yourMessageHere: string;
    messageSent: string;
    messageSentDesc: string;
    messageFailed: string;
    messageFailedDesc: string;
    sendingMessage: string;
    contactInfo: string;
    address: string;
    writeUs: string;
    callUs: string;
    orSchedule: string;
    // Properties for floating contact
    whatsapp: string;
    linkedin: string;
    github: string;
    booking: string;
    directions: string;
  };
  cookies: {
    title: string;
    description: string;
    acceptAll: string;
    decline: string;
    customize: string;
    necessary: string;
    necessaryDesc: string;
    analytics: string;
    analyticsDesc: string;
    marketing: string;
    marketingDesc: string;
    functional: string;
    functionalDesc: string;
    save: string;
    settings: string;
    close: string;
    cookiePolicy: string;
    privacyPolicy: string;
  };
  footer: {
    copyright: string;
    description: string;
    quickLinks: string;
    footerContact: string;
    newsletter: string;
    newsletterDesc: string;
    emailPlaceholder: string;
    subscribe: string;
    builtWith: string;
    and: string;
    downloadCV: string;
    englishCV: string;
    germanCV: string;
    contactUs?: string;
    openingHours?: string;
    weekdayHours?: string;
    weekendHours?: string;
    appointmentsNote?: string;
    stayInformed?: string;
    legalUpdates?: string;
    privacyNote?: string;
    membershipsCertifications?: string;
  };
  heroParallax?: {
    title: string;
    subtitle: string;
    products: {
      mobileApp: string;
      webDev: string;
      uiux: string;
      ecommerce: string;
      ai: string;
      cloud: string;
      devops: string;
      dataAnalytics: string;
      blockchain: string;
      arvr: string;
      customSoftware: string;
      mobileGame: string;
      iot: string;
      api: string;
      cybersecurity: string;
    };
  };
  featuresSection: {
    features: {
      title: string;
      description: string;
      icon:
      | "IconTerminal2"
      | "IconEaseInOut"
      | "IconCurrencyDollar"
      | "IconCloud"
      | "IconRouteAltLeft"
      | "IconHelp"
      | "IconAdjustmentsBolt"
      | "IconHeart"; 
    }[];
  };
  faq: {
    title: string;
    subtitle: string;
    description: string;
    showMore: string;
    showLess: string;
    items: Array<{
      id: string;
      question: string;
      answer: string;
    }>;
  };
  legalServices?: {
    familyLaw?: {
      title?: string;
      description?: string;
      divorce?: { title?: string; description?: string };
      maintenance?: { title?: string; description?: string };
      custody?: { title?: string; description?: string };
    };
    employmentLaw?: {
      title?: string;
      description?: string;
      protection?: { title?: string; description?: string };
      contract?: { title?: string; description?: string };
      warning?: { title?: string; description?: string };
    };
    contractLaw?: {
      title?: string;
      description?: string;
      purchase?: { title?: string; description?: string };
      service?: { title?: string; description?: string };
      work?: { title?: string; description?: string };
    };
    criminalLaw?: {
      title?: string;
      description?: string;
      defense?: { title?: string; description?: string };
      victimProtection?: { title?: string; description?: string };
      juvenile?: { title?: string; description?: string };
    };
    civilLaw?: {
      title?: string;
      description?: string;
      damages?: { title?: string; description?: string };
      disputes?: { title?: string; description?: string };
      general?: { title?: string; description?: string };
    };
    trafficLaw?: {
      title?: string;
      description?: string;
      accident?: { title?: string; description?: string };
      fines?: { title?: string; description?: string };
      license?: { title?: string; description?: string };
    };
    inheritanceLaw?: {
      title?: string;
      description?: string;
      will?: { title?: string; description?: string };
      inheritance?: { title?: string; description?: string };
      estate?: { title?: string; description?: string };
    };
  };
  booking?: {
    title?: string;
    subtitle?: string;
    description?: string;
    bookingButton?: string;
    successMessage?: string;
    backToCalendar?: string;
    nameLabel?: string;
    emailLabel?: string;
    phoneLabel?: string;
    messageLabel?: string;
    bookingTypes?: {
      consultation?: string;
      meeting?: string;
      support?: string;
    };
    durations?: {
      short?: string;
      medium?: string;
      long?: string;
    };
    calendarButton?: string;
    submitButton?: string;
    backButton?: string;
    timeSlots?: string;
    contactInfo?: string;
    selectDate?: string;
    selectTime?: string;
    yourInformation?: string;
    requestingBooking?: string;
    pleaseSelectDate?: string;
  };
  cta: {
    badge: string;
    title: string;
    subtitle: string;
    features: {
      seo: string;
      languages: string;
      themes: string;
      integrations: string;
      quality: string;
      delivery: string;
      support: string;
    };
    buttons: {
      templates: string;
      consultation: string;
    };
    trust: {
      secure: string;
      support: string;
      guarantee: string;
    };
  };
}

export const getDictionary = async (locale: Locale) => dictionaries[locale]()
