/**
 * Unsplash API utility for fetching law firm related images
 * 
 * This utility provides functions to fetch law firm related images from Unsplash
 * NOTE: You'll need to replace the ACCESS_KEY with your actual Unsplash API key
 */

// Replace with your actual Unsplash API access key
const ACCESS_KEY = 'YOUR_UNSPLASH_ACCESS_KEY';

// Law firm related search terms
const LAW_FIRM_TERMS = [
  'lawyer portrait',
  'attorney professional',
  'law office',
  'legal professional',
  'courtroom',
  'law library',
  'legal consultation',
  'law firm interior',
  'lawyer desk',
  'legal documents',
  'law books',
  'scales of justice',
  'modern law office',
  'attorney at work',
  'legal team',
  'law firm meeting',
  'professional lawyer',
  'legal consultation'
];

/**
 * Fetch a random law firm image from Unsplash
 * @param query Optional specific query, otherwise uses random law firm term
 * @param width Desired image width
 * @param height Desired image height
 * @returns Image URL
 */
export const getRandomLawFirmImage = async (
  query?: string,
  width: number = 1200,
  height: number = 800
): Promise<string> => {
  const searchTerm = query || LAW_FIRM_TERMS[Math.floor(Math.random() * LAW_FIRM_TERMS.length)];
  const url = `https://api.unsplash.com/photos/random?query=${encodeURIComponent(searchTerm)}&orientation=landscape&client_id=${ACCESS_KEY}`;
  
  try {
    const response = await fetch(url);
    if (!response.ok) {
      console.error('Error fetching from Unsplash:', response.statusText);
      // Fallback to a default image if the API fails
      return `/images/lawfirm/fallback-${Math.floor(Math.random() * 5) + 1}.jpg`;
    }
    
    const data = await response.json();
    return `${data.urls.raw}&w=${width}&h=${height}&fit=crop&crop=faces,center`;
  } catch (error) {
    console.error('Error fetching Unsplash image:', error);
    // Fallback to a default image if the API fails
    return `/images/lawfirm/fallback-${Math.floor(Math.random() * 5) + 1}.jpg`;
  }
};

/**
 * Fetch multiple law firm images from Unsplash
 * @param count Number of images to fetch
 * @param query Optional specific query, otherwise uses random law firm terms
 * @param width Desired image width
 * @param height Desired image height
 * @returns Array of image URLs
 */
export const getLawFirmImages = async (
  count: number = 6,
  query?: string,
  width: number = 600,
  height: number = 400
): Promise<string[]> => {
  const searchTerm = query || 'law firm,lawyer,attorney';
  const url = `https://api.unsplash.com/photos/random?query=${encodeURIComponent(searchTerm)}&count=${count}&orientation=landscape&client_id=${ACCESS_KEY}`;
  
  try {
    const response = await fetch(url);
    if (!response.ok) {
      console.error('Error fetching from Unsplash:', response.statusText);
      // Return array of fallback images
      return Array(count).fill(null).map((_, i) => 
        `/images/lawfirm/fallback-${(i % 5) + 1}.jpg`
      );
    }
    
    const data = await response.json();
    return data.map((item: any) => 
      `${item.urls.raw}&w=${width}&h=${height}&fit=crop&crop=faces,center`
    );
  } catch (error) {
    console.error('Error fetching Unsplash images:', error);
    // Return array of fallback images
    return Array(count).fill(null).map((_, i) => 
      `/images/lawfirm/fallback-${(i % 5) + 1}.jpg`
    );
  }
};

/**
 * Get a specific category of law firm images
 * @param category Category of law (family, criminal, corporate, etc.)
 * @param count Number of images to fetch
 * @param width Desired image width
 * @param height Desired image height
 * @returns Array of image URLs
 */
export const getLawFirmImagesByCategory = async (
  category: string,
  count: number = 3,
  width: number = 600,
  height: number = 400
): Promise<string[]> => {
  const searchTerm = `${category} law`;
  return getLawFirmImages(count, searchTerm, width, height);
};

/**
 * Generate image URL for static placeholder when Unsplash API is not available
 * @param seed Seed for generating the image
 * @param text Optional text to overlay on the image
 * @param width Image width
 * @param height Image height
 * @returns Placeholder image URL
 */
export const getPlaceholderImage = (
  seed: string,
  text?: string,
  width: number = 600,
  height: number = 400
): string => {
  const baseUrl = 'https://picsum.photos/seed';
  return `${baseUrl}/${encodeURIComponent(seed)}/${width}/${height}`;
};
