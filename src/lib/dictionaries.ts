import { type Dictionary } from './dictionary';

// We'll use dynamic imports for better code splitting
const dictionaries = {
  en: () => import('@/dictionaries/en.json').then(module => ({ ...module.default, prices: {}, packages: {}, faq: {}, serviceSection: {} } as unknown as Dictionary)),
  de: () => import('@/dictionaries/de.json').then(module => ({ ...module.default, prices: {}, packages: {}, faq: {}, serviceSection: {} } as unknown as Dictionary)),
  ru: () => import('@/dictionaries/ru.json').then(module => ({ ...module.default, prices: {}, packages: {}, faq: {}, serviceSection: {} } as unknown as Dictionary)),
  tr: () => import('@/dictionaries/tr.json').then(module => ({ ...module.default, prices: {}, packages: {}, faq: {}, serviceSection: {} } as unknown as Dictionary)),
  ar: () => import('@/dictionaries/ar.json').then(module => ({ ...module.default, prices: {}, packages: {}, faq: {}, serviceSection: {} } as unknown as Dictionary)),
};

export const getDictionary = async (locale: string): Promise<Dictionary> => {
  // Fallback to English if the locale doesn't exist
  return (dictionaries[locale as keyof typeof dictionaries] || dictionaries.en)();
}; 