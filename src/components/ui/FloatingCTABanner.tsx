"use client";

import { motion } from "framer-motion";
import { useI18n } from "@/providers/I18nProvider";
import { type Dictionary } from "@/lib/dictionary";
import { ArrowRightIcon, CheckCircleIcon } from "@heroicons/react/24/solid";
import { useState, useEffect } from "react";

interface FloatingCTABannerProps {
  dictionary: Dictionary["cta"];
}

export const FloatingCTABanner = ({ dictionary }: FloatingCTABannerProps) => {
  const { dir } = useI18n();
  const isRtl = dir === "rtl";
  const [isVisible, setIsVisible] = useState(false);

  // Show banner after user scrolls down a bit
  useEffect(() => {
    const handleScroll = () => {
      setIsVisible(window.scrollY > 300);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const features = [
    dictionary.features.seo,
    dictionary.features.languages,
    dictionary.features.themes,
    dictionary.features.integrations,
    dictionary.features.quality,
    dictionary.features.delivery,
    dictionary.features.support,
  ];

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 100 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`fixed bottom-4 left-1/2 transform -translate-x-1/2 z-40 w-[90%] max-w-6xl ${
        isRtl ? "rtl-section" : ""
      }`}
      style={{
        marginRight: isRtl ? "0" : "120px",
        marginLeft: isRtl ? "120px" : "0",
      }} // Space for floating contact buttons
    >
      <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-lg border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-2xl p-4">
        <div className="flex items-center justify-between gap-4">
          {/* Left Side - Premium Quality Badge */}
          <div className="flex-shrink-0">
            <div className="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-emerald-600 to-blue-600 text-white rounded-xl shadow-lg">
              <div className="flex items-center justify-center">
                <svg
                  className="w-5 h-5 text-yellow-300"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
              <span className="text-sm font-bold whitespace-nowrap">
                {dictionary.features.quality}
              </span>
            </div>
          </div>

          {/* Middle - Scrolling Features */}
          <div className="flex-1 overflow-hidden mx-4">
            <div className="relative">
              <div className="flex animate-scroll-x gap-6">
                {/* First set of features */}
                {features.map((feature, index) => (
                  <div
                    key={`first-${index}`}
                    className="flex items-center gap-2 whitespace-nowrap px-3 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    <CheckCircleIcon className="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
                      {feature}
                    </span>
                  </div>
                ))}
                {/* Duplicate set for seamless loop */}
                {features.map((feature, index) => (
                  <div
                    key={`second-${index}`}
                    className="flex items-center gap-2 whitespace-nowrap px-3 py-1 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    <CheckCircleIcon className="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
                      {feature}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Side - CTA Buttons */}
          <div className="flex-shrink-0 flex gap-2">
            <motion.a
              href="https://www.digify-now.com#templates"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="group inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-sm font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <span className="hidden sm:inline">
                {dictionary.buttons.templates}
              </span>
              <span className="sm:hidden">Templates</span>
              <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300" />
            </motion.a>

            <motion.a
              href="https://www.digify-now.com#contact"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="group inline-flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm font-semibold rounded-lg border border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <span className="hidden sm:inline">
                {dictionary.buttons.consultation}
              </span>
              <span className="sm:hidden">Consult</span>
              <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-0.5 transition-transform duration-300" />
            </motion.a>
          </div>
        </div>
      </div>
    </motion.div>
  );
};
