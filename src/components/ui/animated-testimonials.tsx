"use client";

import { IconArrowLeft, IconArrowRight } from "@tabler/icons-react";
import { Quote } from 'lucide-react';
import { motion, AnimatePresence, useAnimation } from "framer-motion";
import Image from "next/image";
import { useEffect, useState, useRef } from "react";
import { cn } from "@/lib/utils";

type Testimonial = {
  quote: string;
  name: string;
  designation: string;
  src: string;
  property?: string;
  rating?: number;
  date?: string;
  transactionType?: string;
};

export const AnimatedTestimonials = ({
  testimonials,
  autoplay = false,
  pauseOnHover = true,
  className,
}: {
  testimonials: Testimonial[];
  autoplay?: boolean;
  pauseOnHover?: boolean;
  className?: string;
}) => {
  const [active, setActive] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleNext = () => {
    setActive((prev) => (prev + 1) % testimonials.length);
  };

  const handlePrev = () => {
    setActive((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const isActive = (index: number) => {
    return index === active;
  };

  useEffect(() => {
    if (autoplay && !(pauseOnHover && isHovered)) {
      const interval = setInterval(handleNext, 8000);
      return () => clearInterval(interval);
    }
  }, [autoplay, isHovered, pauseOnHover]);
  
  // Handle swipe gestures
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };
  
  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };
  
  const handleTouchEnd = () => {
    if (touchStart - touchEnd > 100) {
      // Swipe left, show next
      handleNext();
    }
    
    if (touchStart - touchEnd < -100) {
      // Swipe right, show previous
      handlePrev();
    }
  };

  const randomRotateY = () => {
    return Math.floor(Math.random() * 21) - 10;
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const shouldShowReadMore = (text: string) => text.length > 150;
  const displayText = (text: string) => {
    if (!shouldShowReadMore(text) || isExpanded) return text;
    return text.slice(0, 150) + "...";
  };

  return (
    <div
      className={cn(
        "max-w-sm md:max-w-5xl mx-auto px-4 md:px-8 lg:px-12 py-6 sm:py-10 md:py-12",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      ref={containerRef}
      role="region"
      aria-roledescription="carousel"
      aria-label="Client testimonials carousel"
    >
      <div className="relative grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-10 md:gap-16">
        <div>
          <div 
            className="relative h-80 sm:h-96 md:h-[500px] w-full z-2"
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            role="presentation"
          >
            <AnimatePresence>
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial.src}
                  initial={{
                    opacity: 0,
                    scale: 0.9,
                    z: -100,
                    rotate: randomRotateY(),
                  }}
                  animate={{
                    opacity: isActive(index) ? 1 : 0.7,
                    scale: isActive(index) ? 1 : 0.95,
                    z: isActive(index) ? 0 : -100,
                    rotate: isActive(index) ? 0 : randomRotateY(),
                    zIndex: isActive(index)
                      ? 999
                      : testimonials.length + 2 - index,
                    y: isActive(index) ? [0, -40, 0] : 0,
                  }}
                  exit={{
                    opacity: 0,
                    scale: 0.9,
                    z: 100,
                    rotate: randomRotateY(),
                  }}
                  transition={{
                    duration: 0.4,
                    ease: "easeInOut",
                  }}
                  className="absolute inset-0 origin-bottom"
                >
                  <Image
                    src={testimonial.src}
                    alt={`Photo of ${testimonial.name}, ${testimonial.designation}${testimonial.property ? ` - ${testimonial.property}` : ''}`}
                    width={800}
                    height={800}
                    draggable={false}
                    className="h-full w-full rounded-2xl sm:rounded-3xl object-cover object-center shadow-xl"
                    priority={index === active}
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
        <div className="flex flex-col justify-between py-4 mt-4 sm:mt-0 md:pl-6">
          {/* Text content container */}
          <div 
            className="bg-slate-50/70 dark:bg-slate-800/60 backdrop-blur-sm p-6 rounded-xl shadow-md flex-grow mb-6"
            aria-live="polite"
            aria-atomic="true"
          >
          <motion.div
            key={active}
            initial={{
              y: 20,
              opacity: 0,
            }}
            animate={{
              y: 0,
              opacity: 1,
            }}
            exit={{
              y: -20,
              opacity: 0,
            }}
            transition={{
              duration: 0.2,
              ease: "easeInOut",
            }}
          >
            <h3 className="text-xl sm:text-2xl font-bold text-foreground dark:text-white" id={`testimonial-${active}-author`}>
              {testimonials[active].name}
            </h3>
            <p className="text-xs sm:text-sm text-muted-foreground dark:text-gray-400">
              {testimonials[active].designation}
              {testimonials[active].property && (
                <span className="ml-1">- {testimonials[active].property}</span>
              )}
            </p>
            {testimonials[active].date && (
              <p className="text-xs text-muted-foreground dark:text-gray-400 mt-1">
                {testimonials[active].date} - {testimonials[active].transactionType || "Client"}
              </p>
            )}
            <div className="mt-4 sm:mt-6 space-y-3 sm:space-y-4">
              <div className="relative">
                <Quote className="absolute -top-2 -left-3 w-10 h-10 text-slate-300 dark:text-slate-600 opacity-50" aria-hidden="true" />
                <motion.p 
                  className="text-base sm:text-lg text-muted-foreground dark:text-gray-300 relative z-10"
                  aria-labelledby={`testimonial-${active}-author`}
                >
                {displayText(testimonials[active].quote)
                  .split(" ")
                  .map((word, index) => (
                    <motion.span
                      key={index}
                      initial={{
                        filter: "blur(10px)",
                        opacity: 0,
                        y: 5,
                      }}
                      animate={{
                        filter: "blur(0px)",
                        opacity: 1,
                        y: 0,
                      }}
                      transition={{
                        duration: 0.2,
                        ease: "easeInOut",
                        delay: 0.02 * index,
                      }}
                      className="inline-block"
                    >
                      {word}&nbsp;
                    </motion.span>
                  ))}
                </motion.p>
              </div> {/* End relative div for quote icon and text */}
              {shouldShowReadMore(testimonials[active].quote) && (
                <button
                  onClick={toggleExpand}
                  className="mt-4 text-sm font-semibold text-primary hover:text-primary/90 dark:text-sky-400 dark:hover:text-sky-300 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-sky-400 dark:focus:ring-offset-gray-900"
                  aria-expanded={isExpanded}
                  aria-controls={`testimonial-${active}-content`}
                >
                  {isExpanded ? "Read Less" : "Read More"}
                </button>
              )}
            </div>
          </motion.div>
          </div> {/* End Text content container */}
          <div className="flex items-center gap-3 mt-auto" role="group" aria-label="Testimonial navigation controls">
            <button
              onClick={handlePrev}
              className="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-secondary dark:bg-blue-700 border border-gray-200 dark:border-blue-500 flex items-center justify-center group/button hover:bg-secondary/80 dark:hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400 dark:focus:ring-blue-300 shadow-md"
              aria-label="Previous testimonial"
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handlePrev();
                }
              }}
            >
              <IconArrowLeft className="h-4 w-4 sm:h-6 sm:w-6 text-foreground dark:text-white group-hover/button:rotate-12 transition-transform duration-300" aria-hidden="true" />
            </button>
            
            <div className="flex gap-2" role="tablist" aria-label="Select a testimonial">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActive(index)}
                  className={`w-2.5 h-2.5 rounded-full transition-all duration-300 ${isActive(index) ? "bg-primary scale-125" : "bg-gray-300 dark:bg-gray-700"}`}
                  aria-label={`Go to testimonial ${index + 1}`}
                  aria-selected={isActive(index)}
                  role="tab"
                  tabIndex={isActive(index) ? 0 : -1}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      setActive(index);
                    }
                  }}
                />
              ))}
            </div>
            
            <button
              onClick={handleNext}
              className="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-secondary dark:bg-blue-700 border border-gray-200 dark:border-blue-500 flex items-center justify-center group/button hover:bg-secondary/80 dark:hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400 dark:focus:ring-blue-300 shadow-md"
              aria-label="Next testimonial"
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleNext();
                }
              }}
            >
              <IconArrowRight className="h-4 w-4 sm:h-6 sm:w-6 text-foreground dark:text-white group-hover/button:-rotate-12 transition-transform duration-300" aria-hidden="true" />
            </button>
          </div>
          
          <div className="text-center text-xs text-gray-500 dark:text-gray-400 mt-2 md:hidden" aria-hidden="true">
            Swipe to view more testimonials
          </div>
        </div>
      </div>
    </div>
  );
};
 