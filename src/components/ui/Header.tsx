'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import { motion, AnimatePresence } from 'framer-motion'
import { useI18n, type Locale } from '@/providers/I18nProvider'
import { NavigationLink } from '@/types'
import { LanguageSelector } from '../ui/LanguageSelector'

// Updated navigation links with translation keys
const defaultNavigationLinks = [
  { key: 'home', href: '#hero' },
  { key: 'services', href: '#services' },
  { key: 'about', href: '#about' },
  { key: 'testimonials', href: '#testimonials' },
  { key: 'contact', href: '#contact' },
]

// Supported languages with their display names
const languages = [
  { code: 'en', name: 'English' },
  { code: 'de', name: '<PERSON><PERSON><PERSON>' },
  { code: 'ru', name: 'Русский' },
  { code: 'tr', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { code: 'ar', name: 'العربية' },
]

// Contact details
const contactInfo = {
  calendlyLink: "https://calendly.com/your-business", // Replace with your actual Calendly link
  phone: "+****************", // Replace with your actual phone number
  email: "<EMAIL>", // Replace with your actual email
};

interface HeaderProps {
  navigation?: NavigationLink[];
}

export function Header({
  navigation = defaultNavigationLinks as NavigationLink[],
}: HeaderProps) {
  // Initialize scrolled state to false
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const { theme, setTheme } = useTheme();
  const { locale, setLocale, t, dir } = useI18n();

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Tastatursteuerung für Navigationselemente
  const handleKeyDown = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      action();
    }
  };

  // Handle scroll events to change header appearance
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      // Set scrolled to true if scrolled more than 10px, otherwise false
      setScrolled(offset > 10);
    };

    // Check initial scroll position on mount
    handleScroll();

    // Add scroll event listener
    window.addEventListener("scroll", handleScroll);

    // Show header after a short delay
    setTimeout(() => {
      setIsVisible(true);
    }, 1000);

    // Cleanup function to remove event listener
    return () => window.removeEventListener("scroll", handleScroll);
  }, []); // Empty dependency array ensures this runs only on mount and unmount

  // Toggle theme
  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  // Update layout for RTL languages
  const rtlClass = dir === "rtl" ? "rtl-layout" : "";

  // Simple neutral header styling with glass effect
  const headerBg = scrolled
    ? "bg-black/30 backdrop-blur-lg shadow-lg border-b border-white/10"
    : "bg-transparent";

  // Determine text color based on scroll state
  const textColor = "text-white";

  // Adjust padding based on scroll state for a sleeker look when scrolled
  const headerPadding = scrolled ? "py-2" : "py-3";

  const supportedLocales = ["en", "de", "ru", "tr", "ar"];

  return (
    <header
      // Use fixed positioning, always at the top (top-0)
      // Apply transitions for smooth changes in background, padding, etc.
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${headerBg} ${textColor} ${headerPadding} ${rtlClass} ${
        isVisible ? "opacity-100" : "opacity-0"
      } pointer-events-${isVisible ? "auto" : "none"}`}
      role="banner"
      aria-label="Site navigation"
    >
      <div className="container mx-auto px-6 flex items-center justify-between h-12">
        {/* Logo & Chip container */}
        <div className="flex items-center gap-2 h-full">
          {/* Simple home link when scrolled */}
          <AnimatePresence>
            {scrolled && (
              <motion.div
                key="homelink"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.3 }}
              >
                <Link
                  href="#hero"
                  className="text-white hover:text-gray-200 font-medium"
                  aria-label="Back to top"
                >
                  Home
                </Link>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Enhanced Desktop Navigation with hover effects */}
        <nav
          className="hidden md:flex items-center gap-6"
          aria-label="Main navigation"
        >
          {navigation.map((link) => (
            <Link
              key={link.key || ""}
              href={link.href || "#"}
              className="relative px-3 py-1.5 text-white/90 hover:text-white text-sm font-medium transition-all duration-300 whitespace-nowrap group rounded-md hover:bg-white/10"
            >
              <span className="relative z-10">
                {t(`nav.${link.key || ""}`) ||
                  (link.key
                    ? link.key.charAt(0).toUpperCase() + link.key.slice(1)
                    : "Link")}
              </span>

              {/* Animated underline effect on hover */}
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-emerald-400 to-white group-hover:w-full transition-all duration-300 rounded-full opacity-0 group-hover:opacity-100"></span>
            </Link>
          ))}

          {/* Phone number with green indicator */}
          <a
            href={`tel:${contactInfo.phone}`}
            className="flex items-center gap-2 px-4 py-2 text-white/90 hover:text-white text-sm font-medium transition-all duration-300 whitespace-nowrap group rounded-md hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/50 focus:bg-white/10"
            aria-label="Call our office at ${contactInfo.phone}"
          >
            <span className="relative flex h-2 w-2 mr-1">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
              <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
            </span>
            {contactInfo.phone}
          </a>

          {/* Language selector */}
          <LanguageSelector
            currentLocale={locale}
            locales={supportedLocales}
            onChange={(code) => setLocale(code as Locale)}
            className="text-white/80 hover:text-white"
          />

          {/* Enhanced Theme toggle with animation */}
          <motion.button
            type="button"
            onClick={toggleTheme}
            className="p-2 rounded-full text-white/90 hover:text-white hover:bg-white/10 transition-all duration-300 relative overflow-hidden focus:outline-none focus:ring-2 focus:ring-white/50"
            aria-label={
              theme === "dark" ? "Switch to light mode" : "Switch to dark mode"
            }
            aria-pressed={theme === "dark"}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-violet-400/20 opacity-0 hover:opacity-100 transition-opacity duration-300 rounded-full" />

            {theme === "dark" ? (
              <motion.svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                initial={{ rotate: 0 }}
                animate={{ rotate: [0, 15, 0] }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                />
              </motion.svg>
            ) : (
              <motion.svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                initial={{ rotate: 0 }}
                animate={{ rotate: [0, -15, 0] }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                />
              </motion.svg>
            )}
          </motion.button>
        </nav>

        {/* Mobile Menu Toggle - Shown only on mobile */}
        <button
          className="flex md:hidden items-center bg-gray-800/70 rounded-lg p-3 text-white border border-white/10 focus:outline-none focus:ring-2 focus:ring-white/50"
          onClick={toggleMobileMenu}
          aria-expanded={mobileMenuOpen}
          aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
          aria-controls="mobile-menu"
          onKeyDown={(e) => handleKeyDown(e, toggleMobileMenu)}
        >
          {mobileMenuOpen ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16m-7 6h7"
              />
            </svg>
          )}
        </button>

        {/* Mobile navigation menu */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="md:hidden absolute top-[48px] right-[40px] w-auto bg-gray-800/95 border-t border-white/10 backdrop-blur-lg shadow-lg rounded-xl z-50"
              id="mobile-menu"
              role="navigation"
              aria-label="Mobile Navigation"
            >
              <div className="container mx-auto px-4 py-3 flex flex-col items-start">
                <div className="grid grid-cols-1 gap-2 justify-items-start">
                  {navigation.map((link, index) => (
                    <motion.div
                      key={link.key}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2, delay: index * 0.05 }}
                    >
                      <Link
                        href={link.href}
                        onClick={() => setMobileMenuOpen(false)}
                        className="py-2.5 text-sm text-white/90 hover:text-white font-medium whitespace-nowrap px-3 flex items-center gap-2 rounded-md hover:bg-white/10 transition-all duration-300 relative overflow-hidden group focus:outline-none focus:ring-2 focus:ring-white/50"
                        role="menuitem"
                        tabIndex={0}
                        aria-label={t(`nav.${link.key}`)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter" || e.key === " ") {
                            e.preventDefault();
                            setMobileMenuOpen(false);
                          }
                        }}
                      >
                        {/* Subtle shine effect */}
                        <motion.div
                          className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/10 to-transparent"
                          initial={{ x: "-100%" }}
                          transition={{ duration: 1, ease: "easeInOut" }}
                        />

                        <span className="relative z-10">
                          {t(`nav.${link.key}`)}
                        </span>

                        {/* Animated indicator */}
                        <motion.div
                          className="absolute bottom-0 left-0 h-[2px] bg-gradient-to-r from-emerald-400 to-emerald-400 rounded-full"
                          initial={{ width: "0%" }}
                          whileHover={{ width: "100%" }}
                          transition={{ duration: 0.3 }}
                        />
                      </Link>
                    </motion.div>
                  ))}

                  {/* Enhanced Book a consultation button */}
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.2,
                      delay: navigation.length * 0.05,
                    }}
                    className="mt-2 pt-2 border-t border-white/10"
                  >
                    <motion.a
                      href={contactInfo.calendlyLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      onClick={() => setMobileMenuOpen(false)}
                      className="relative inline-flex items-center justify-center gap-2 bg-emerald-600/90 hover:bg-emerald-700/90 text-white px-4 py-2 rounded-full text-sm font-medium shadow-md border border-white/10 overflow-hidden group focus:outline-none focus:ring-2 focus:ring-white/50"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      role="menuitem"
                      tabIndex={0}
                      aria-label={t("nav.bookConsultation")}
                      aria-describedby="book-consultation-description"
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ") {
                          e.preventDefault();
                          setMobileMenuOpen(false);
                        }
                      }}
                    >
                      {/* Shine effect */}
                      <motion.div
                        className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                        initial={{ x: "-100%" }}
                        transition={{ duration: 1, ease: "easeInOut" }}
                      />

                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 group-hover:rotate-12 transition-transform duration-300"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                      <span className="relative z-10">
                        {t("nav.bookConsultation")}
                      </span>

                      {/* Status indicator */}
                      <motion.div
                        className="absolute top-2 right-2 flex items-center justify-center"
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <span className="relative flex h-2 w-2">
                          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                          <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                        </span>
                      </motion.div>
                    </motion.a>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </header>
  );
}