'use client'

import { Fragment } from 'react'
import { Menu, Transition } from '@headlessui/react'
import { GlobeAltIcon } from '@heroicons/react/24/outline'
import { cn } from '@/lib/utils'

interface LanguageSelectorProps {
  currentLocale: string
  locales: string[]
  onChange: (locale: string) => void
  className?: string
}

// Language display names for accessibility
const getLanguageDisplayName = (code: string): string => {
  const languageNames: Record<string, string> = {
    'en': 'English',
    'de': 'Deutsch',
    'ru': 'Русский',
    'tr': 'Türkçe',
    'ar': 'العربية',
  };
  return languageNames[code] || code.toUpperCase();
};

export function LanguageSelector({ currentLocale, locales, onChange, className }: LanguageSelectorProps) {
  return (
    <Menu as="div" className="relative">
      <Menu.Button 
        className={cn(
          "flex items-center gap-2 rounded-md p-2 hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/50",
          className
        )}
        aria-label="Select language"
        aria-expanded="false"
        aria-haspopup="true"
      >
        <GlobeAltIcon className="h-5 w-5" aria-hidden="true" />
        <span className="sr-only">Current language: {getLanguageDisplayName(currentLocale)}</span>
      </Menu.Button>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items 
          className="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-gray-900/90 py-1 shadow-lg ring-1 ring-white/10 focus:outline-none backdrop-blur-sm z-50"
          aria-label="Language menu"
        >
          {locales.map((locale) => (
            <Menu.Item key={locale}>
              {({ active }) => (
                <button
                  onClick={() => onChange(locale)}
                  className={cn(
                    active ? 'bg-white/10' : '',
                    locale === currentLocale ? 'text-white font-medium' : 'text-white/80',
                    'block w-full px-4 py-2 text-left text-sm focus:outline-none focus:bg-white/20'
                  )}
                  role="menuitem"
                  aria-current={locale === currentLocale ? 'true' : undefined}
                  lang={locale}
                >
                  <span className="flex items-center justify-between">
                    {getLanguageDisplayName(locale)}
                    {locale === currentLocale && (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </span>
                </button>
              )}
            </Menu.Item>
          ))}
        </Menu.Items>
      </Transition>
    </Menu>
  )
} 