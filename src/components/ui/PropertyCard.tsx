'use client'

import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import {
  MapPin,
  Maximize,
  Bed,
  Bath,
  Car,
  Video,
  Calendar,
  Award,
  Flame,
  Tag,
  Home,
} from "lucide-react";

export interface PropertyDetails {
  id: string;
  title: string;
  price: string;
  address: string;
  description: string;
  image: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  garages?: number;
  featured?: boolean;
  type: "sale" | "rent";
  tags?: string[];
  status?:
    | "new"
    | "featured"
    | "hot"
    | "open-house"
    | "reduced"
    | "exclusive"
    | "sold";
  virtualTour?: boolean;
  yearBuilt?: number;
  sqft?: number;
}

interface PropertyCardProps {
  property: PropertyDetails;
  priority?: boolean;
  onClick?: () => void;
}

export const PropertyCard: React.FC<PropertyCardProps> = ({
  property,
  priority = false,
  onClick,
}) => {
  const {
    title,
    price,
    address,
    image,
    bedrooms,
    bathrooms,
    area,
    garages = 0,
    featured = false,
    type,
    tags = [],
    status,
    virtualTour = false,
    yearBuilt,
    sqft,
  } = property;

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick && onClick();
    }
  };

  return (
    <motion.div
      className="group flex flex-col bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 h-full focus-within:ring-2 focus-within:ring-blue-500"
      whileHover={{ y: -5 }}
      onClick={onClick}
      tabIndex={0}
      role="article"
      aria-label={`${title} - ${price} - ${address}`}
      onKeyDown={handleKeyDown}
    >
      {/* Property Image */}
      <div className="relative w-full h-64 overflow-hidden">
        <Image
          src={image}
          alt={`Property image of ${title} at ${address}`}
          fill
          priority={priority}
          className="object-cover group-hover:scale-110 transition-transform duration-500"
        />

        {/* Property Type & Status Tags */}
        <div className="absolute top-4 left-4 flex flex-wrap gap-2" aria-hidden="false">
          <span
            className={`px-3 py-1 rounded-full text-xs font-semibold ${
              type === "sale"
                ? "bg-blue-500 text-white"
                : "bg-amber-500 text-white"
            }`}
            role="status"
          >
            {type === "sale" ? "For Sale" : "For Rent"}
          </span>

          {status && (
            <span
              className={`px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1 ${
                status === "new"
                  ? "bg-green-500 text-white"
                  : status === "featured"
                  ? "bg-purple-500 text-white"
                  : status === "hot"
                  ? "bg-red-500 text-white"
                  : status === "open-house"
                  ? "bg-teal-500 text-white"
                  : status === "reduced"
                  ? "bg-orange-500 text-white"
                  : status === "exclusive"
                  ? "bg-indigo-500 text-white"
                  : status === "sold"
                  ? "bg-gray-500 text-white"
                  : ""
              }`}
              role="status"
            >
              {status === "new" && <Tag size={12} aria-hidden="true" />}
              {status === "featured" && <Award size={12} aria-hidden="true" />}
              {status === "hot" && <Flame size={12} aria-hidden="true" />}
              {status === "open-house" && <Calendar size={12} aria-hidden="true" />}
              {status === "reduced" && <Tag size={12} aria-hidden="true" />}
              {status === "exclusive" && <Award size={12} aria-hidden="true" />}
              {status === "sold" && <Tag size={12} aria-hidden="true" />}
              {status.charAt(0).toUpperCase() +
                status.slice(1).replace("-", " ")}
            </span>
          )}

          {virtualTour && (
            <span className="px-3 py-1 rounded-full text-xs font-semibold bg-blue-400 text-white flex items-center gap-1">
              <Video size={12} aria-hidden="true" />
              Virtual Tour
            </span>
          )}
        </div>

        {/* Price Tag */}
        <div 
          className="absolute bottom-4 left-4 bg-white dark:bg-gray-800 px-4 py-1 rounded-full text-sm font-bold"
          aria-label={`Price: ${price}`}
        >
          {price}
        </div>
      </div>

      {/* Property Details */}
      <div className="flex flex-col p-5 flex-grow">
        <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white" id={`property-${property.id}-title`}>
          {title}
        </h3>

        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
          <MapPin size={16} className="mr-1" aria-hidden="true" />
          <span>{address}</span>
        </div>

        {/* Property Features */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm text-gray-600 dark:text-gray-300 my-3" aria-label="Property features">
          <div className="flex items-center">
            <Bed size={16} className="mr-1" aria-hidden="true" />
            <span>
              {bedrooms} {bedrooms === 1 ? "Bedroom" : "Bedrooms"}
            </span>
          </div>

          <div className="flex items-center">
            <Bath size={16} className="mr-1" aria-hidden="true" />
            <span>
              {bathrooms} {bathrooms === 1 ? "Bathroom" : "Bathrooms"}
            </span>
          </div>

          <div className="flex items-center">
            <Maximize size={16} className="mr-1" aria-hidden="true" />
            <span>{area} m² area</span>
          </div>

          {garages > 0 && (
            <div className="flex items-center">
              <Car size={16} className="mr-1" aria-hidden="true" />
              <span>
                {garages} {garages === 1 ? "Garage" : "Garages"}
              </span>
            </div>
          )}

          {sqft && (
            <div className="flex items-center">
              <Home size={16} className="mr-1" aria-hidden="true" />
              <span>{sqft} sqft</span>
            </div>
          )}

          {yearBuilt && (
            <div className="flex items-center">
              <Calendar size={16} className="mr-1" aria-hidden="true" />
              <span>Built {yearBuilt}</span>
            </div>
          )}
        </div>

        {/* Tags */}
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-2" aria-label="Property tags">
            {tags.map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded text-gray-600 dark:text-gray-300"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Action button */}
      <div className="p-5 pt-0">
        <button
          className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2"
          onClick={(e) => {
            e.stopPropagation();
            onClick && onClick();
          }}
          aria-labelledby={`property-${property.id}-title`}
        >
          View Details
        </button>
      </div>
    </motion.div>
  );
};

export default PropertyCard;
