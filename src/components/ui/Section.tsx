import React from 'react'
import { cn } from '@/lib/utils'

interface SectionProps {
  id?: string;
  title?: React.ReactNode;
  subtitle?: string;
  description?: string;
  children?: React.ReactNode;
  className?: string;
  containerClassName?: string;
  titleClassName?: string;
  subtitleClassName?: string;
  descriptionClassName?: string;
  headingLevel?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  subheadingLevel?: 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  // Accessibility attributes
  role?: string;
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  tabIndex?: number;
}

export const Section = ({
  id,
  title,
  subtitle,
  description,
  children,
  className = "",
  containerClassName = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
  titleClassName = "",
  subtitleClassName = "",
  descriptionClassName = "",
  headingLevel = 'h2',
  subheadingLevel = 'h3',
  role,
  'aria-label': ariaLabel,
  'aria-labelledby': ariaLabelledby,
  'aria-describedby': ariaDescribedby,
  tabIndex,
}: SectionProps) => {
  return (
    <section
      id={id}
      className={cn("py-20 w-full relative overflow-hidden", className)}
      role={role}
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledby}
      aria-describedby={ariaDescribedby}
      tabIndex={tabIndex}
    >
      <div className={cn(containerClassName, "w-full")}>
        {/* Section header */}
        {(title || subtitle) && (
          <div className="text-center mb-12">
            {title && (
              React.createElement(
                headingLevel,
                {
                  className: cn(
                    "text-3xl sm:text-4xl font-bold mb-2",
                    titleClassName
                  ),
                  id: id ? `${id}-heading` : undefined
                },
                title
              )
            )}
            {subtitle && (
              React.createElement(
                subheadingLevel,
                {
                  className: cn("text-lg font-medium", subtitleClassName),
                  id: id ? `${id}-subheading` : undefined
                },
                subtitle
              )
            )}
            {description && (
              <p className={cn("mt-4 text-gray-600 dark:text-gray-400", descriptionClassName)}>
                {description}
              </p>
            )}
          </div>
        )}
        {children}
      </div>
    </section>
  );
}; 