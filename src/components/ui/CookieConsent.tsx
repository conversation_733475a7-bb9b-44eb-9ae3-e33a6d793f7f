'use client'

import { useState, useEffect } from 'react'
import { useI18n } from '@/providers/I18nProvider'
import { cn } from '@/lib/utils'
import { X } from 'lucide-react'

type CookiePreferences = {
  necessary: boolean
  analytics: boolean
  marketing: boolean
  functional: boolean
}

export function CookieConsent() {
  const { t } = useI18n()
  const [open, setOpen] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [cookiePreferences, setCookiePreferences] = useState<CookiePreferences>({
    necessary: true, // Always required
    analytics: true,
    marketing: true,
    functional: true,
  })

  // Check if consent was previously given
  useEffect(() => {
    const consent = localStorage.getItem('cookie-consent')
    if (!consent) {
      const timer = setTimeout(() => {
        setOpen(true)
      }, 1000) // Show banner after 1 second
      return () => clearTimeout(timer)
    }
  }, [])

  // Handle accepting all cookies
  const acceptAll = () => {
    setCookiePreferences({
      necessary: true,
      analytics: true,
      marketing: true,
      functional: true,
    })
    
    localStorage.setItem('cookie-consent', JSON.stringify({
      necessary: true,
      analytics: true,
      marketing: true,
      functional: true,
      timestamp: new Date().toISOString(),
    }))
    
    setOpen(false)
  }

  // Handle declining non-essential cookies
  const decline = () => {
    setCookiePreferences({
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false,
    })
    
    localStorage.setItem('cookie-consent', JSON.stringify({
      necessary: true,
      analytics: false,
      marketing: false,
      functional: false,
      timestamp: new Date().toISOString(),
    }))
    
    setOpen(false)
  }

  // Handle saving custom preferences
  const savePreferences = () => {
    localStorage.setItem('cookie-consent', JSON.stringify({
      ...cookiePreferences,
      timestamp: new Date().toISOString(),
    }))
    
    setOpen(false)
    setShowSettings(false)
  }

  // Toggle cookie settings panel
  const toggleSettings = () => {
    setShowSettings(!showSettings)
  }

  // Open cookie banner manually (for settings link in footer)
  const openCookieSettings = () => {
    setOpen(true)
    setShowSettings(true)
  }

  // Handle change of individual cookie preferences
  const handleCookiePreferenceChange = (type: keyof CookiePreferences) => {
    if (type === 'necessary') return // Cannot change necessary cookies

    setCookiePreferences(prev => ({
      ...prev,
      [type]: !prev[type]
    }))
  }

  // Attach to window for external access
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).openCookieSettings = openCookieSettings
    }
    return () => {
      if (typeof window !== 'undefined') {
        delete (window as any).openCookieSettings
      }
    }
  }, [])

  if (!open) return null

  return (
    <div 
      className="fixed bottom-0 left-0 right-0 z-50"
      role="dialog"
      aria-modal="true"
      aria-labelledby="cookie-consent-title"
      aria-describedby="cookie-consent-description"
      tabIndex={-1}
    >
      <div className="bg-white dark:bg-gray-800 shadow-lg border-t border-gray-200 dark:border-gray-700 p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          {!showSettings ? (
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2" id="cookie-consent-title">
                  {t('Cookie Consent')}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 md:mb-0 max-w-3xl" id="cookie-consent-description">
                  {t('We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking "Accept All", you consent to our use of cookies.')}
                </p>
              </div>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={decline}
                  className="px-4 py-2 text-sm text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                  tabIndex={0}
                >
                  {t('Decline')}
                </button>
                <button
                  onClick={toggleSettings}
                  className="px-4 py-2 text-sm text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                >
                  {t('Customize')}
                </button>
                <button
                  onClick={acceptAll}
                  className="px-4 py-2 text-sm text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                >
                  {t('Accept All')}
                </button>
              </div>
            </div>
          ) : (
            <div className="relative">
              <button
                onClick={() => setShowSettings(false)}
                className="absolute right-0 top-0 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 p-2 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                aria-label={t('Close cookie settings')}
              >
                <X size={20} aria-hidden="true" />
              </button>
              
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4" id="cookie-settings-title">
                {t('Cookie Settings')}
              </h3>
              
              <div className="space-y-4 mb-6">
                <div className="flex items-start gap-2">
                  <div className="flex items-center h-5 mt-1">
                    <input
                      id="necessary"
                      type="checkbox"
                      checked={cookiePreferences.necessary}
                      disabled
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600 cursor-not-allowed opacity-60"
                    />
                  </div>
                  <div className="ms-2">
                    <label htmlFor="necessary" className="text-sm font-medium text-gray-900 dark:text-white">
                      {t('Necessary Cookies')}
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {t('These cookies are essential for the website to function properly and cannot be disabled.')}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-2">
                  <div className="flex items-center h-5 mt-1">
                    <input
                      id="analytics"
                      type="checkbox"
                      checked={cookiePreferences.analytics}
                      onChange={() => handleCookiePreferenceChange('analytics')}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                    />
                  </div>
                  <div className="ms-2">
                    <label htmlFor="analytics" className="text-sm font-medium text-gray-900 dark:text-white">
                      {t('Analytics Cookies')}
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {t('These cookies help us understand how visitors interact with our website and help us improve our services.')}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-2">
                  <div className="flex items-center h-5 mt-1">
                    <input
                      id="marketing"
                      type="checkbox"
                      checked={cookiePreferences.marketing}
                      onChange={() => handleCookiePreferenceChange('marketing')}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                    />
                  </div>
                  <div className="ms-2">
                    <label htmlFor="marketing" className="text-sm font-medium text-gray-900 dark:text-white">
                      {t('Marketing Cookies')}
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {t('These cookies are used to track visitors across websites to display relevant advertisements.')}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-2">
                  <div className="flex items-center h-5 mt-1">
                    <input
                      id="functional"
                      type="checkbox"
                      checked={cookiePreferences.functional}
                      onChange={() => handleCookiePreferenceChange('functional')}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
                    />
                  </div>
                  <div className="ms-2">
                    <label htmlFor="functional" className="text-sm font-medium text-gray-900 dark:text-white">
                      {t('Functional Cookies')}
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {t('These cookies enable enhanced functionality and personalization on our website.')}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  <a href="/cookie-policy" className="underline hover:text-gray-700 dark:hover:text-gray-300 mr-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:rounded-sm">
                    {t('Cookie Policy')}
                  </a>
                  <a href="/privacy-policy" className="underline hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:rounded-sm">
                    {t('Privacy Policy')}
                  </a>
                </div>
                
                <button
                  onClick={savePreferences}
                  className="px-4 py-2 text-sm text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                >
                  {t('Save Preferences')}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CookieConsent 