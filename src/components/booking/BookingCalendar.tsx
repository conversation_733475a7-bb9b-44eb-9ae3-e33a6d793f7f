"use client";

import { useState, useEffect } from "react";
import { Section } from "@/components/ui/Section";
import { Button } from "@/components/ui/Button";

type BookingCalendarProps = {
  dictionary?: {
    title?: string;
    subtitle?: string;
    description?: string;
    bookingButton?: string;
    successMessage?: string;
    backToCalendar?: string;
    nameLabel?: string;
    emailLabel?: string;
    phoneLabel?: string;
    messageLabel?: string;
    bookingTypes?: {
      consultation?: string;
      meeting?: string;
      support?: string;
    };
    durations?: {
      short?: string;
      medium?: string;
      long?: string;
    };
    calendarButton?: string;
    submitButton?: string;
    backButton?: string;
    timeSlots?: string;
    contactInfo?: string;
  };
}

// Generate hours (9 AM to 5 PM)
const generateHours = () => {
  const hours = [];
  for (let hour = 9; hour <= 20; hour++) {
    hours.push(hour);
  }
  return hours;
};

// Generate minutes (0 or 30 minutes)
const generateMinutes = () => {
  return [0, 30];
};

// Format date as YYYY-MM-DD
const formatDate = (date: Date) => {
  return date.toISOString().split('T')[0];
};

// Format month for display
const formatMonth = (date: Date) => {
  return date.toLocaleString('default', { month: 'long', year: 'numeric' });
};

// Format time in AM/PM format
const formatTime = (hour: number, minute: number) => {
  const period = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour % 12 || 12;
  const displayMinute = minute === 0 ? '00' : '30';
  return `${displayHour}:${displayMinute} ${period}`;
};

// Get number of days in a month
const getDaysInMonth = (year: number, month: number) => {
  return new Date(year, month + 1, 0).getDate();
};

// Generate calendar days for a month
const generateCalendarDays = (date: Date) => {
  const year = date.getFullYear();
  const month = date.getMonth();
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const daysInMonth = getDaysInMonth(year, month);
  const firstDay = new Date(year, month, 1).getDay();
  
  const days = [];
  
  // Add days from previous month to fill the first week
  const prevMonth = month === 0 ? 11 : month - 1;
  const prevMonthYear = month === 0 ? year - 1 : year;
  const daysInPrevMonth = getDaysInMonth(prevMonthYear, prevMonth);
  
  for (let i = 0; i < firstDay; i++) {
    const day = daysInPrevMonth - firstDay + i + 1;
    const date = new Date(prevMonthYear, prevMonth, day);
    days.push({
      date,
      isCurrentMonth: false,
      isSelectable: false,
    });
  }
  
  // Add days for current month
  for (let i = 1; i <= daysInMonth; i++) {
    const date = new Date(year, month, i);
    const isPast = date < today;
    
    days.push({
      date,
      isCurrentMonth: true,
      isSelectable: !isPast,
    });
  }
  
  // Add days from next month to complete the calendar grid
  const totalCells = 42;
  const remainingCells = totalCells - days.length;
  
  const nextMonth = month === 11 ? 0 : month + 1;
  const nextMonthYear = month === 11 ? year + 1 : year;
  
  for (let i = 1; i <= remainingCells; i++) {
    const date = new Date(nextMonthYear, nextMonth, i);
    days.push({
      date,
      isCurrentMonth: false,
      isSelectable: false,
    });
  }
  
  return days;
};

const BookingCalendar = ({ dictionary }: BookingCalendarProps) => {
  // States for the booking calendar
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [calendarDays, setCalendarDays] = useState<Array<{
    date: Date;
    isCurrentMonth: boolean;
    isSelectable: boolean;
  }>>([]);
  
  const [selectedHour, setSelectedHour] = useState(10); // Default to 10 AM
  const [selectedMinute, setSelectedMinute] = useState(0); // Default to 0 minutes
  
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  
  const [contactInfo, setContactInfo] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
  });
  
  // Generate calendar days when the month changes
  useEffect(() => {
    setCalendarDays(generateCalendarDays(currentMonth));
  }, [currentMonth]);
  
  // Navigate to previous month
  const goToPreviousMonth = () => {
    const previousMonth = new Date(currentMonth);
    previousMonth.setMonth(previousMonth.getMonth() - 1);
    setCurrentMonth(previousMonth);
  };
  
  // Navigate to next month
  const goToNextMonth = () => {
    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    setCurrentMonth(nextMonth);
  };
  
  // Handle date selection
  const handleDateSelect = (day: {date: Date, isCurrentMonth: boolean, isSelectable: boolean}) => {
    if (day.isSelectable) {
      setSelectedDate(day.date);
    }
  };
  
  // Increment hour
  const incrementHour = () => {
    setSelectedHour((prev) => (prev >= 20 ? 9 : prev + 1));
  };
  
  // Decrement hour
  const decrementHour = () => {
    setSelectedHour((prev) => (prev <= 9 ? 20 : prev - 1));
  };
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setContactInfo((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  
  // Handle booking form submission
  const handleBookingSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedDate) return;
    
    // Get the date and time for the appointment
    const appointmentDate = new Date(selectedDate);
    appointmentDate.setHours(selectedHour, selectedMinute, 0, 0);
    
    // Here you would typically send the booking data to your backend
    // For demo purposes, we'll just show a success message
    
    // Log the booking details
    console.log("Booking Request:", {
      date: appointmentDate.toLocaleString(),
      formattedTime: formatTime(selectedHour, selectedMinute),
      contactInfo,
    });
    
    // Show success message
    setShowSuccessMessage(true);
  };
  
  // Reset the booking form
  const handleReset = () => {
    setSelectedDate(null);
    setSelectedHour(10);
    setSelectedMinute(0);
    setContactInfo({
      name: '',
      email: '',
      phone: '',
      message: '',
    });
    setShowSuccessMessage(false);
  };

  return (
    <Section
      className="py-20 px-4 md:px-8 bg-gradient-to-br from-secondary/20 via-transparent to-primary/20"
    >
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold mb-4 text-gray-900 tracking-tight">{dictionary?.title || "Book an Appointment"}</h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">{dictionary?.subtitle || "Schedule a meeting with our team"}</p>
        </div>
        
        {showSuccessMessage ? (
          <div className="bg-white rounded-lg shadow-lg p-8 text-center my-10 max-w-2xl mx-auto">
            <div className="text-green-600 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold mb-4">{dictionary?.successMessage || "Booking Request Sent!"}</h3>
            <p className="mb-6 text-gray-600">
              Thank you for your booking request. We will confirm your appointment shortly.
            </p>
            <button 
              onClick={handleReset} 
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors shadow-md"
            >
              {dictionary?.backToCalendar || "Book Another Meeting"}
            </button>
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-xl p-6 md:p-8 border border-gray-100">
            <form onSubmit={handleBookingSubmit} className="flex flex-col lg:flex-row gap-8 lg:gap-12">
              {/* Calendar Column */}
              <div className="flex-1">
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-2xl font-semibold text-gray-900">Select Date</h3>
                    <div className="flex items-center space-x-3">
                      <button
                        type="button"
                        onClick={goToPreviousMonth} 
                        className="p-2.5 bg-gray-50 hover:bg-blue-50 text-blue-600 rounded-lg transition-colors duration-200"
                        aria-label="Previous month"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </button>
                      <button
                        type="button"
                        onClick={goToNextMonth} 
                        className="p-2.5 bg-gray-50 hover:bg-blue-50 text-blue-600 rounded-lg transition-colors duration-200"
                        aria-label="Next month"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  
                  <div className="bg-blue-50 text-center py-3 px-4 rounded-lg text-blue-700 font-medium text-xl mb-6">
                    {formatMonth(currentMonth)}
                  </div>
                  
                  <div className="relative">
                    {/* Subtiler Kalender im Hintergrund */}
                    <div className="absolute inset-0 opacity-5 pointer-events-none">
                      <div className="w-full h-full bg-blue-500 rounded-xl">
                        <div className="grid h-full">
                          <div className="border-b border-white/20"></div>
                          <div className="border-b border-white/20"></div>
                          <div className="border-b border-white/20"></div>
                          <div className="border-b border-white/20"></div>
                          <div className="border-b border-white/20"></div>
                        </div>
                        <div className="grid h-full grid-cols-7 absolute inset-0">
                          <div className="border-r border-white/20"></div>
                          <div className="border-r border-white/20"></div>
                          <div className="border-r border-white/20"></div>
                          <div className="border-r border-white/20"></div>
                          <div className="border-r border-white/20"></div>
                          <div className="border-r border-white/20"></div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-7 gap-2 mb-3 relative z-10">
                      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                        <div key={day} className="text-center text-gray-600 text-base font-medium py-2">
                          {day}
                        </div>
                      ))}
                    </div>
                    
                    <div className="grid grid-cols-7 gap-2 relative z-10">
                      {calendarDays.map((day, index) => (
                        <button
                          key={index}
                          type="button"
                          onClick={() => handleDateSelect(day)}
                          disabled={!day.isSelectable}
                          className={`
                            py-3 rounded-lg text-base lg:text-lg font-medium transition-all duration-200
                            ${!day.isCurrentMonth ? 'text-gray-300 opacity-50' : ''}
                            ${!day.isSelectable ? 'text-gray-300 cursor-not-allowed opacity-50' : ''}
                            ${day.date.toDateString() === selectedDate?.toDateString() 
                              ? 'bg-blue-500 text-white shadow-md hover:bg-blue-600 transform scale-105' 
                              : 'hover:bg-blue-50 hover:text-blue-700'}
                          `}
                        >
                          {day.date.getDate()}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Time and Contact Form Column */}
              <div className="flex-1">
                <div className="mb-8">
                  <h3 className="text-2xl font-semibold mb-6 text-gray-900">Select Time</h3>
                  <div className="flex items-center justify-center space-x-2 md:space-x-8 mb-8 flex-wrap">
                    <div className="relative flex flex-col items-center">
                      <button 
                        type="button" 
                        onClick={incrementHour} 
                        className="p-2.5 rounded-full hover:bg-blue-100 transition-colors mb-2 text-blue-600"
                        aria-label="Increase hour"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                      <div className="text-2xl md:text-4xl font-bold w-14 h-14 md:w-20 md:h-20 flex items-center justify-center bg-white border border-gray-200 rounded-xl shadow-sm">
                        {selectedHour % 12 || 12}
                      </div>
                      <button 
                        type="button" 
                        onClick={decrementHour} 
                        className="p-2.5 rounded-full hover:bg-blue-100 transition-colors mt-2 text-blue-600"
                        aria-label="Decrease hour"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                    
                    <div className="text-2xl md:text-4xl font-bold">:</div>
                    
                    <div className="relative flex flex-col items-center">
                      <button
                        type="button"
                        onClick={() => setSelectedMinute(selectedMinute === 0 ? 30 : 0)}
                        className="text-2xl md:text-4xl font-bold w-14 h-14 md:w-20 md:h-20 flex items-center justify-center bg-blue-50 border border-blue-300 text-blue-700 rounded-xl shadow-sm transition-colors hover:bg-blue-100"
                      >
                        {selectedMinute === 0 ? '00' : '30'}
                      </button>
                    </div>
                    
                    <div className="flex flex-col items-center">
                      <div className="text-lg md:text-2xl font-bold w-14 h-14 md:w-20 md:h-20 flex items-center justify-center bg-white border border-gray-200 rounded-xl shadow-sm">
                        {selectedHour >= 12 ? 'PM' : 'AM'}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="mb-8">
                  <h3 className="text-2xl font-semibold mb-5 text-gray-900">Your Information</h3>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                        {dictionary?.nameLabel || "Your Name"} *
                      </label>
                      <input
                        id="name"
                        name="name"
                        type="text"
                        required
                        value={contactInfo.name}
                        onChange={handleInputChange}
                        className="block w-full rounded-lg border border-gray-300 shadow-sm p-3 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        {dictionary?.emailLabel || "Email Address"} *
                      </label>
                      <input
                        id="email"
                        name="email"
                        type="email"
                        required
                        value={contactInfo.email}
                        onChange={handleInputChange}
                        className="block w-full rounded-lg border border-gray-300 shadow-sm p-3 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                        {dictionary?.phoneLabel || "Phone Number"}
                      </label>
                      <input
                        id="phone"
                        name="phone"
                        type="tel"
                        value={contactInfo.phone}
                        onChange={handleInputChange}
                        className="block w-full rounded-lg border border-gray-300 shadow-sm p-3 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="mt-6">
                  <button 
                    type="submit" 
                    className="w-full px-6 py-3 bg-blue-500 text-white text-lg font-medium rounded-xl hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-md"
                    disabled={!selectedDate}
                  >
                    {dictionary?.bookingButton || "Request Booking"}
                  </button>
                  <p className="text-sm text-gray-500 mt-3 text-center">
                    {selectedDate ? (
                      <>You are requesting a booking for <span className="font-medium text-blue-600">{formatTime(selectedHour, selectedMinute)}</span> on <span className="font-medium text-blue-600">{selectedDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}</span></>
                    ) : (
                      <>Please select a date to continue</>
                    )}
                  </p>
                </div>
              </div>
            </form>
          </div>
        )}
      </div>
    </Section>
  );
};

export default BookingCalendar;
