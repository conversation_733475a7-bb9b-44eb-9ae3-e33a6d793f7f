'use client'

import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Section } from "../ui/Section";
import {
  ScrollAnimation,
  ScrollAnimationGroup,
} from "@/components/ui/ScrollAnimation";
import "./scrollbar-hide.css";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { useInView } from "react-intersection-observer";
import { type Dictionary } from "@/lib/dictionary";
import {
  Users,
  Briefcase,
  FileText,
  ShieldCheck,
  FileSignature,
  Baby,
  Wrench,
  Home as HomeIcon,
  Receipt,
  FileWarning,
  Scroll,
  Gavel,
  Building,
  Heart,
  HeartPulse,
  Sword as SwordIcon,
  Car,
  PenTool,
  AlertTriangle,
  ShoppingCart,
  FileCode,
  Shield,
  BanknoteIcon,
  HeartHandshake,
  Scissors,
  Award,
  BarChart,
  ScrollText,
  Edit2,
  Scale,
  Clipboard<PERSON>heck,
  Shield<PERSON><PERSON>t,
  Hammer,
} from "lucide-react";

// Define types to match dictionary structure
interface LawServiceItem {
  name: string;
  icon: React.ElementType;
  description: string;
}

interface LawService {
  title: string;
  description: string;
  icon: React.ElementType;
  items: LawServiceItem[];
}

// Legal services data with multilingual support
// This will be populated from the dictionary in the component
const generateServiceData = (
  dictionary: Dictionary
): Record<string, ServiceData> => {
  return {
    familyLaw: {
      title: dictionary.legalServices?.familyLaw?.title || "Family Law",
      description:
        dictionary.legalServices?.familyLaw?.description ||
        "Expert legal support for families navigating complex emotional situations",
      items: [
        {
          name:
            dictionary.legalServices?.familyLaw?.divorce?.title || "Divorce",
          description:
            dictionary.legalServices?.familyLaw?.divorce?.description ||
            "Guidance through the dissolution of marriage with compassion",
          icon: Scissors,
        },
        {
          name:
            dictionary.legalServices?.familyLaw?.maintenance?.title ||
            "Maintenance",
          description:
            dictionary.legalServices?.familyLaw?.maintenance?.description ||
            "Ensuring fair financial support for family members",
          icon: BanknoteIcon,
        },
        {
          name:
            dictionary.legalServices?.familyLaw?.custody?.title ||
            "Custody & Visitation",
          description:
            dictionary.legalServices?.familyLaw?.custody?.description ||
            "Protecting the best interests of children in family disputes",
          icon: HeartHandshake,
        },
      ],
    },
    employmentLaw: {
      title: dictionary.legalServices?.employmentLaw?.title || "Employment Law",
      description:
        dictionary.legalServices?.employmentLaw?.description ||
        "Comprehensive legal advice for employees and employers.",
      icon: Briefcase,
      items: [
        {
          name:
            dictionary.legalServices?.employmentLaw?.protection?.title ||
            "Dismissal Protection",
          icon: ShieldCheck,
          description:
            dictionary.legalServices?.employmentLaw?.protection?.description ||
            "Representation in dismissal and severance cases.",
        },
        {
          name:
            dictionary.legalServices?.employmentLaw?.contract?.title ||
            "Employment Contracts",
          icon: FileText,
          description:
            dictionary.legalServices?.employmentLaw?.contract?.description ||
            "Review and drafting of employment contracts.",
        },
        {
          name:
            dictionary.legalServices?.employmentLaw?.warning?.title ||
            "Warnings",
          icon: AlertTriangle,
          description:
            dictionary.legalServices?.employmentLaw?.warning?.description ||
            "Advice on warnings and employment law conflicts.",
        },
      ],
    },
    contractLaw: {
      title: dictionary.legalServices?.contractLaw?.title || "Contract Law",
      description:
        dictionary.legalServices?.contractLaw?.description ||
        "Creation, review, and optimization of contracts of all kinds.",
      icon: FileText,
      items: [
        {
          name:
            dictionary.legalServices?.contractLaw?.purchase?.title ||
            "Purchase Contracts",
          icon: FileText,
          description:
            dictionary.legalServices?.contractLaw?.purchase?.description ||
            "Design and review of purchase contracts.",
        },
        {
          name:
            dictionary.legalServices?.contractLaw?.service?.title ||
            "Service Contracts",
          icon: Hammer,
          description:
            dictionary.legalServices?.contractLaw?.service?.description ||
            "Legally secure service contracts for companies and individuals.",
        },
        {
          name:
            dictionary.legalServices?.contractLaw?.work?.title ||
            "Work Contracts",
          icon: Hammer,
          description:
            dictionary.legalServices?.contractLaw?.work?.description ||
            "Advice on work contracts and their implementation.",
        },
      ],
    },
    criminalLaw: {
      title: dictionary.legalServices?.criminalLaw?.title || "Criminal Law",
      description:
        dictionary.legalServices?.criminalLaw?.description ||
        "Defense and committed advice in criminal matters.",
      icon: ShieldCheck,
      items: [
        {
          name:
            dictionary.legalServices?.criminalLaw?.defense?.title || "Defense",
          icon: ShieldCheck,
          description:
            dictionary.legalServices?.criminalLaw?.defense?.description ||
            "Criminal defense in investigation and main proceedings.",
        },
        {
          name:
            dictionary.legalServices?.criminalLaw?.victimProtection?.title ||
            "Victim Protection",
          icon: Heart,
          description:
            dictionary.legalServices?.criminalLaw?.victimProtection
              ?.description ||
            "Representation of victims in criminal proceedings.",
        },
        {
          name:
            dictionary.legalServices?.criminalLaw?.juvenile?.title ||
            "Juvenile Criminal Law",
          icon: Users,
          description:
            dictionary.legalServices?.criminalLaw?.juvenile?.description ||
            "Specialized advice on juvenile criminal law.",
        },
      ],
    },
    civilLaw: {
      title: dictionary.legalServices?.civilLaw?.title || "Civil Law",
      description:
        dictionary.legalServices?.civilLaw?.description ||
        "Advice and representation in all civil law matters.",
      icon: Award,
      items: [
        {
          name: dictionary.legalServices?.civilLaw?.damages?.title || "Damages",
          icon: BarChart,
          description:
            dictionary.legalServices?.civilLaw?.damages?.description ||
            "Enforcement and defense of claims for damages.",
        },
        {
          name:
            dictionary.legalServices?.civilLaw?.disputes?.title ||
            "Contract Disputes",
          icon: FileText,
          description:
            dictionary.legalServices?.civilLaw?.disputes?.description ||
            "Resolution of conflicts arising from contracts.",
        },
        {
          name:
            dictionary.legalServices?.civilLaw?.general?.title ||
            "General Civil Law",
          icon: Award,
          description:
            dictionary.legalServices?.civilLaw?.general?.description ||
            "Competent support in all civil law issues.",
        },
      ],
    },
    trafficLaw: {
      title: dictionary.legalServices?.trafficLaw?.title || "Traffic Law",
      description:
        dictionary.legalServices?.trafficLaw?.description ||
        "Competent help with fines, accident settlement, and driver's license matters.",
      icon: Car,
      items: [
        {
          name:
            dictionary.legalServices?.trafficLaw?.accident?.title ||
            "Accident Settlement",
          icon: Wrench,
          description:
            dictionary.legalServices?.trafficLaw?.accident?.description ||
            "Enforcement of your claims after traffic accidents.",
        },
        {
          name:
            dictionary.legalServices?.trafficLaw?.fines?.title ||
            "Fine Matters",
          icon: ShieldAlert,
          description:
            dictionary.legalServices?.trafficLaw?.fines?.description ||
            "Defense against fines and driving bans.",
        },
        {
          name:
            dictionary.legalServices?.trafficLaw?.license?.title ||
            "Driver's License Law",
          icon: ClipboardCheck,
          description:
            dictionary.legalServices?.trafficLaw?.license?.description ||
            "Advice on driver's license revocation and medical psychological assessment.",
        },
      ],
    },
    inheritanceLaw: {
      title:
        dictionary.legalServices?.inheritanceLaw?.title || "Inheritance Law",
      description:
        dictionary.legalServices?.inheritanceLaw?.description ||
        "Succession planning and representation in inheritance disputes.",
      icon: ScrollText,
      items: [
        {
          name:
            dictionary.legalServices?.inheritanceLaw?.will?.title ||
            "Will Creation",
          icon: Edit2,
          description:
            dictionary.legalServices?.inheritanceLaw?.will?.description ||
            "Creation and review of wills and inheritance contracts.",
        },
        {
          name:
            dictionary.legalServices?.inheritanceLaw?.inheritance?.title ||
            "Compulsory Portion Rights",
          icon: Scale,
          description:
            dictionary.legalServices?.inheritanceLaw?.inheritance
              ?.description ||
            "Assertion and defense of compulsory portion claims.",
        },
        {
          name:
            dictionary.legalServices?.inheritanceLaw?.estate?.title ||
            "Estate Distribution",
          icon: Users,
          description:
            dictionary.legalServices?.inheritanceLaw?.estate?.description ||
            "Guidance in the distribution of the estate.",
        },
      ],
    },
  };
};

interface ServiceCardProps {
  title: string;
  description: string;
  icon: React.ElementType;
  items: { name: string; icon: React.ElementType; description: string }[];
  index: number;
  inView: boolean;
  serviceKey: string;
  onClick: () => void;
  className?: string;
}

const ServiceCard = ({
  title,
  description,
  icon,
  items,
  index,
  inView,
  serviceKey,
  onClick,
  className = "",
}: ServiceCardProps) => {
  const getColorScheme = () => {
    return {
      iconBg:
        "bg-gradient-to-tr from-blue-50 via-white to-blue-100 dark:from-slate-800 dark:via-slate-900 dark:to-slate-700 border border-blue-100 dark:border-slate-700 shadow-sm",
      iconColor: "text-blue-700 dark:text-blue-300",
      checkBg: "bg-blue-100/60 dark:bg-blue-900/30",
      checkIconColor: "text-blue-700 dark:text-blue-300",
    };
  };

  const colorScheme = getColorScheme();

  return (
    <motion.div
      className={`group cursor-pointer ${className} bg-white dark:bg-slate-900 border border-blue-100 dark:border-slate-700 rounded-2xl shadow-lg p-8 min-w-[340px] transition-transform hover:-translate-y-1 hover:shadow-xl flex flex-col gap-4`}
      whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)" }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 30 }}
      animate={inView ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      onClick={onClick}
    >
      <div className="w-full h-full p-6 rounded-2xl bg-white dark:bg-gray-800 shadow-sm hover:shadow-xl transition-all duration-300 relative overflow-hidden flex flex-col items-center sm:items-start text-center sm:text-left">
        {/* Icon */}
        <div
          className={`${colorScheme.iconBg} w-16 h-16 rounded-2xl flex items-center justify-center mb-5 mx-auto sm:mx-0`}
        >
          {/* Render the icon component directly, applying color and size */}
          {icon &&
            React.createElement(icon, {
              className: `${colorScheme.iconColor} w-8 h-8`,
            })}
        </div>

        {/* Title */}
        <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-3">
          {title}
        </h3>

        {/* Description */}
        <p className="text-gray-600 dark:text-gray-300 mb-6 line-clamp-2">
          {description}
        </p>

        {/* Features */}
        <div className="space-y-2 mb-6 w-full">
          {items.slice(0, 3).map((item, i) => (
            <div key={i} className="flex items-center gap-2 justify-start">
              <div
                className={`${colorScheme.checkBg} w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0`}
              >
                {item.icon &&
                  React.createElement(item.icon, {
                    className: `w-3 h-3 ${colorScheme.checkIconColor}`,
                  })}
              </div>
              <span className="text-sm text-gray-700 dark:text-gray-300 font-medium">
                {item.name}
              </span>
            </div>
          ))}
        </div>

        {/* Learn More */}
      </div>
    </motion.div>
  );
};

interface ServiceDetailsViewProps {
  serviceKey: string;
  service: ServiceData;
  onBack: () => void;
  dictionary: Dictionary;
}

const ServiceDetailsView = ({
  serviceKey,
  service,
  onBack,
  dictionary,
}: ServiceDetailsViewProps) => {
  const [activeTab, setActiveTab] = useState<"overview" | "features">(
    "overview"
  );
  const { ref: detailsRef, inView: detailsInView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const getServiceIcon = (serviceKey: string) => {
    switch (serviceKey) {
      case "familyLaw":
        return <Users className="w-6 h-6" />;
      case "employmentLaw":
        return <Briefcase className="w-6 h-6" />;
      case "contractLaw":
        return <FileText className="w-6 h-6" />;
      case "criminalLaw":
        return <ShieldCheck className="w-6 h-6" />;
      default:
        return <Briefcase className="w-6 h-6" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.4 }}
      className="mt-8"
      ref={detailsRef}
    >
      <button
        onClick={onBack}
        className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 mb-6 transition-colors"
      >
        <span aria-hidden="true">←</span>
        <span>
          {dictionary.serviceSection?.backToServices || "Back to all services"}
        </span>
      </button>

      <div className="flex flex-col md:flex-row items-center mb-10">
        <div className="w-24 h-24 mb-6 md:mb-0 md:mr-8 rounded-lg border border-gray-200 dark:border-gray-700 flex items-center justify-center bg-white dark:bg-gray-800">
          <div className="text-gray-600 dark:text-gray-400 scale-[3.5]">
            {getServiceIcon(serviceKey)}
          </div>
        </div>
        <div className="text-center md:text-left">
          <h2 className="text-2xl sm:text-3xl font-bold mb-3 text-gray-900 dark:text-white">
            {service.title}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl">
            {service.description}
          </p>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-xl p-6 sm:p-8 shadow-sm">
        <div className="flex border-b border-gray-200 dark:border-gray-700 mb-6">
          <button
            onClick={() => setActiveTab("overview")}
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "overview"
                ? "text-gray-800 dark:text-gray-200 border-b-2 border-gray-600 dark:border-gray-400"
                : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/50"
            }`}
          >
            {dictionary.serviceSection?.overview || "Overview"}
          </button>
          <button
            onClick={() => setActiveTab("features")}
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "features"
                ? "text-gray-800 dark:text-gray-200 border-b-2 border-gray-600 dark:border-gray-400"
                : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/50"
            }`}
          >
            {dictionary.serviceSection?.features || "Features"}
          </button>
        </div>

        <div className="min-h-[300px]">
          {activeTab === "overview" && (
            <div className="space-y-6">
              <p className="text-gray-700 dark:text-gray-300 text-base sm:text-lg leading-relaxed">
                {service.description}
              </p>
            </div>
          )}

          {activeTab === "features" && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {service.items.map((item, i) => (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, y: 10 }}
                    animate={detailsInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ duration: 0.3, delay: i * 0.05 }}
                    className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="p-2 rounded-md bg-white dark:bg-gray-800 shadow-sm">
                      {/* Render the Lucide icon component directly */}
                      {item.icon &&
                        React.createElement(item.icon, {
                          className:
                            "w-6 h-6 text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors",
                        })}
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {item.name}
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {item.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

interface ServiceData {
  title: string;
  description: string;
  icon?: React.ElementType;
  items: Array<{
    name: string;
    description: string;
    icon: React.ElementType;
  }>;
}

interface ServiceSectionProps {
  dictionary: Dictionary;
}

export const ServiceSection = ({ dictionary }: ServiceSectionProps) => {
  // Generate service data from dictionary
  const serviceData = generateServiceData(dictionary);
  const { ref: servicesRef, inView: servicesInView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // State to track which service is expanded in detail view
  const [expandedService, setExpandedService] = useState<string | null>(null);

  // State to track if user has scrolled the services list
  const [hasScrolled, setHasScrolled] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const getServiceIcon = (serviceKey: string): React.ElementType => {
    switch (serviceKey) {
      case "familyLaw":
        return Users;
      case "employmentLaw":
        return Briefcase;
      case "contractLaw":
        return FileText;
      case "criminalLaw":
        return ShieldCheck;
      default:
        return Briefcase;
    }
  };

  // Define services to display
  const services = [
    { key: "familyLaw", data: serviceData.familyLaw },
    { key: "employmentLaw", data: serviceData.employmentLaw },
    { key: "contractLaw", data: serviceData.contractLaw },
    { key: "criminalLaw", data: serviceData.criminalLaw },
    { key: "civilLaw", data: serviceData.civilLaw },
    { key: "trafficLaw", data: serviceData.trafficLaw },
    { key: "inheritanceLaw", data: serviceData.inheritanceLaw },
  ];

  // Handle service card click
  const handleServiceClick = (serviceKey: string) => {
    setExpandedService(serviceKey);
    // Scroll to top of section
    document.getElementById("services")?.scrollIntoView({ behavior: "smooth" });
  };

  // Handle back button click in detail view
  const handleBackClick = () => {
    setExpandedService(null);
  };

  // Handle scroll events on the services container
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;

    const handleScroll = () => {
      if (scrollContainer && scrollContainer.scrollLeft > 10 && !hasScrolled) {
        setHasScrolled(true);
      }
    };

    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener("scroll", handleScroll);
      }
    };
  }, [hasScrolled]);

  return (
    <Section
      id="services"
      className="py-12 sm:py-16 md:py-24 bg-white dark:bg-gray-900 relative w-full overflow-hidden"
    >
      {/* Simpler background elements */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-700 to-transparent opacity-70" />

      <div className="container mx-auto px-4">
        <AnimatePresence mode="wait">
          {!expandedService ? (
            // Grid view of service cards
            <motion.div
              key="service-grid"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.4 }}
            >
              {/* Mobile view title and description */}
              <div className="block sm:hidden mb-8 w-full">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold mb-4">
                    {dictionary.serviceSection?.title || "Our Services"}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mx-auto">
                    {dictionary.serviceSection?.description ||
                      "We offer a comprehensive range of services to help your business grow and succeed."}
                  </p>
                </div>

                {/* Mobile: Single column layout with staggered animations */}
                <ScrollAnimationGroup
                  direction="bottom"
                  staggerDelay={0.08}
                  distanceOffset={30}
                  className="flex flex-col gap-y-6 px-2"
                  childClassName="flex justify-center h-full"
                >
                  {services.map((service, index) => (
                    <ServiceCard
                      key={service.key}
                      title={service.data?.title || ""}
                      description={service.data?.description || ""}
                      icon={getServiceIcon(service.key)}
                      items={service.data?.items || []}
                      index={index}
                      inView={true} // For mobile, cards are immediately in view or animated by ScrollAnimationGroup
                      serviceKey={service.key}
                      onClick={() => handleServiceClick(service.key)}
                      className="w-full"
                    />
                  ))}
                </ScrollAnimationGroup>
              </div>

              {/* Desktop: Horizontal scroll layout */}
              <div className="hidden sm:block mt-12">
                <div className="text-center mb-12">
                  <h2 className="text-4xl md:text-5xl font-bold mb-4">
                    {dictionary.serviceSection?.title ||
                      "Our Core Competencies"}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    {dictionary.serviceSection?.description ||
                      "Discover our areas of expertise where we provide you with expert support."}
                  </p>
                </div>

                <div
                  ref={servicesRef} // Use servicesRef here for the desktop container
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 w-full max-w-7xl mx-auto px-4"
                >
                  {services.map((service, index) => (
                    <ServiceCard
                      key={service.key}
                      title={service.data?.title || ""}
                      description={service.data?.description || ""}
                      icon={getServiceIcon(service.key)}
                      items={service.data?.items || []}
                      index={index}
                      inView={servicesInView} // servicesInView is for the container
                      serviceKey={service.key}
                      onClick={() => handleServiceClick(service.key)}
                      className="h-full" // Added flex-shrink-0 to prevent cards from shrinking
                    />
                  ))}
                </div>
              </div>

              {dictionary.serviceSection?.viewAll && (
                <div className="mt-16 text-center">
                  <motion.a
                    href="#contact"
                    className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium rounded-full transition-all duration-300 shadow-lg hover:shadow-xl"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {dictionary.serviceSection?.viewAll}
                    <span aria-hidden="true" className="ml-1">
                      →
                    </span>
                  </motion.a>
                </div>
              )}
            </motion.div>
          ) : (
            // Detailed view of selected service
            <motion.div
              key="service-detail"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.4 }}
            >
              {/* Find the selected service data */}
              {(() => {
                const selectedService = services.find(
                  (s) => s.key === expandedService
                );
                if (!selectedService || !selectedService.data) return null;

                return (
                  <ServiceDetailsView
                    serviceKey={selectedService.key}
                    service={selectedService.data}
                    onBack={handleBackClick}
                    dictionary={dictionary}
                  />
                );
              })()}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </Section>
  );
};

// Card content component removed as it's not being used
