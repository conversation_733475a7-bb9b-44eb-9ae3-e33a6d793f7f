'use client'

import React, { useRef, useEffect, useState } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import Image from "next/image";
import { type Dictionary } from "@/lib/dictionary";


interface HeroSectionProps {
  dictionary: Dictionary["hero"];
}

// Spark particles component that responds to scrolling
const SparkParticles = () => {
  // Reference to the container for scroll tracking
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"],
  });

  // Generate random sparks with different properties
  const generateSparks = (count: number) => {
    return Array.from({ length: count }).map((_, i) => {
      // Random properties for each spark
      const size = Math.random() * 3 + 1;
      const initialX = Math.random() * 100;
      const initialY = Math.random() * 100;
      const speed = Math.random() * 15 + 5;
      const delay = Math.random() * 2;
      const duration = Math.random() * 10 + 10;
      const opacity = Math.random() * 0.5 + 0.2;

      // Create parallax effect based on position
      const parallaxStrength = Math.random() * 100 + 50;
      const xParallax = useTransform(
        scrollYProgress,
        [0, 1],
        [initialX, initialX + (Math.random() * 2 - 1) * parallaxStrength]
      );
      const yParallax = useTransform(
        scrollYProgress,
        [0, 1],
        [initialY, initialY + (Math.random() * 2 - 1) * parallaxStrength]
      );

      return {
        id: i,
        size,
        initialX,
        initialY,
        speed,
        delay,
        duration,
        opacity,
        xParallax,
        yParallax,
      };
    });
  };

  // Create small and large sparks with different properties
  const smallSparks = generateSparks(40); // More small sparks for a richer effect
  const largeSparks = generateSparks(15); // Fewer large sparks for accent

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 overflow-hidden pointer-events-none"
    >
      {smallSparks.map((spark) => (
        <motion.div
          key={`small-${spark.id}`}
          className="absolute rounded-full bg-white"
          style={{
            width: spark.size,
            height: spark.size,
            left: `${spark.initialX}%`,
            top: `${spark.initialY}%`,
            x: spark.xParallax,
            y: spark.yParallax,
            opacity: spark.opacity,
          }}
          animate={{
            y: [0, -spark.speed * 4],
            opacity: [0, spark.opacity, 0],
          }}
          transition={{
            duration: spark.duration,
            delay: spark.delay,
            repeat: Infinity,
            ease: "linear",
          }}
        />
      ))}

      {largeSparks.map((spark) => (
        <motion.div
          key={`large-${spark.id}`}
          className="absolute rounded-full bg-gradient-to-r from-blue-400/20 to-violet-400/20"
          style={{
            width: spark.size * 8,
            height: spark.size * 8,
            left: `${spark.initialX}%`,
            top: `${spark.initialY}%`,
            x: spark.xParallax,
            y: spark.yParallax,
            opacity: spark.opacity * 0.75,
            filter: `blur(${spark.size * 1.5}px)`,
          }}
          animate={{
            y: [0, -spark.speed * 8],
            x: [(Math.random() - 0.5) * 35, (Math.random() - 0.5) * 55],
            opacity: [0, spark.opacity, 0],
            scale: [0.2, 1, 0.8, 0.2],
          }}
          transition={{
            duration: spark.duration * 1.5,
            delay: spark.delay,
            repeat: Infinity,
            ease: "linear",
          }}
        />
      ))}
    </div>
  );
};
// Carousel background for hero section
const heroImages = [
  "/images/lawyers/hero_bg_0.jpg",
  "/images/lawyers/hero_bg_1.jpg",
  "/images/lawyers/hero_bg_2.jpg"
];

const CarouselBackground = () => {
  const [current, setCurrent] = useState(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    timeoutRef.current = setTimeout(() => {
      setCurrent((prev) => (prev + 1) % heroImages.length);
    }, 6000);
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [current]);

  return (
    <div className="absolute inset-0 w-full h-full">
      {heroImages.map((src, idx) => (
        <motion.div
          key={src}
          initial={{ opacity: 0 }}
          animate={{ opacity: idx === current ? 1 : 0 }}
          transition={{ duration: 1.2, ease: "easeInOut" }}
          className="absolute inset-0 w-full h-full"
          aria-hidden={idx !== current}
          style={{ pointerEvents: "none" }}
        >
          <Image
            src={src}
            alt="Kanzlei Hintergrund"
            fill
            priority={idx === 0}
            className="object-cover object-center w-full h-full"
            sizes="100vw"
            quality={90}
          />
        </motion.div>
      ))}
      {/* Indicator dots */}
      <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-2 z-10">
        {heroImages.map((_, idx) => (
          <span
            key={idx}
            className={`w-3 h-3 rounded-full bg-white transition-all duration-300 ${current === idx ? "opacity-90 scale-110" : "opacity-50 scale-90"}`}
            aria-label={current === idx ? "Aktives Hintergrundbild" : undefined}
            aria-current={current === idx ? "true" : undefined}
          />
        ))}
      </div>
    </div>
  );
};

export const HeroSection = ({ dictionary }: HeroSectionProps) => {
  const sectionRef = useRef<HTMLElement>(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"],
  });

  // Create a transform scale for the background image that increases as you scroll
  const imageScale = useTransform(scrollYProgress, [0, 1], [1, 1.2]);

  return (
    <section
      ref={sectionRef}
      id="hero"
      className="relative w-full text-white min-h-screen flex flex-col items-center justify-center overflow-hidden"
    >
      {/* WCAG-Siegel: Mobil oben links, kompakt */}
      <div
        className="absolute top-20 left-4 z-30 focus:outline-none focus:ring-4 focus:ring-blue-300 w-16 h-16 md:w-28 md:h-28 md:top-24 md:left-auto md:right-12 transition-all md:block"
        aria-label="WCAG 2.0 Konform"
        tabIndex={0}
      >
        {" "}
        {/* Modernes, hochwertiges Siegel */}
        <div className="relative w-16 h-16 md:w-28 md:h-28 flex items-center justify-center select-none">
          <svg
            viewBox="0 0 112 112"
            width="100%"
            height="100%"
            className="drop-shadow-lg"
            aria-label="WCAG 2.0 Konform Siegel"
            role="img"
          >
            {/* Goldener Kreis mit Glanz */}
            <defs>
              <radialGradient id="gold-gradient" cx="50%" cy="50%" r="50%">
                <stop offset="0%" stopColor="#fffbe8" />
                <stop offset="60%" stopColor="#ffe082" />
                <stop offset="100%" stopColor="#bfa340" />
              </radialGradient>
              <linearGradient id="shine" x1="0" y1="0" x2="1" y2="1">
                <stop offset="0%" stopColor="#fff" stopOpacity="0.7" />
                <stop offset="60%" stopColor="#fff" stopOpacity="0" />
              </linearGradient>
            </defs>
            <circle
              cx="56"
              cy="56"
              r="50"
              fill="url(#gold-gradient)"
              stroke="#e6c558"
              strokeWidth="4"
            />
            {/* Glanz-Effekt */}
            <ellipse
              cx="45"
              cy="38"
              rx="28"
              ry="10"
              fill="url(#shine)"
            />
            {/* Häkchen im Kreis */}
            <circle cx="56" cy="56" r="22" fill="#fff" opacity="0.15" />

            {/* Text */}
            <text
              x="56"
              y="50"
              textAnchor="middle"
              fontSize="17"
              fontWeight="bold"
              fill="#6d4c00"
              fontFamily="'Inter', Arial, sans-serif"
              letterSpacing="1"
            >
              WCAG
            </text>
            <text
              x="56"
              y="67"
              textAnchor="middle"
              fontSize="15"
              fontWeight="bold"
              fill="#6d4c00"
              fontFamily="'Inter', Arial, sans-serif"
            >
              2.0
            </text>
            <text
              x="56"
              y="86"
              textAnchor="middle"
              fontSize="10"
              fill="#7b6a3d"
              fontFamily="'Inter', Arial, sans-serif"
              letterSpacing="0.5"
            >
              KONFORM
            </text>
            {/* Sterne */}
            <g>
              <g transform="translate(41,91)">
                <polygon points="5,0 6.18,3.63 10,4.09 7,6.91 7.9,10.68 5,8.8 2.1,10.68 3,6.91 0,4.09 3.82,3.63" fill="#ffd700" stroke="#bfa340" strokeWidth="0.5" />
              </g>
              <g transform="translate(56,93)">
                <polygon points="5,0 6.18,3.63 10,4.09 7,6.91 7.9,10.68 5,8.8 2.1,10.68 3,6.91 0,4.09 3.82,3.63" fill="#ffd700" stroke="#bfa340" strokeWidth="0.5" />
              </g>
              <g transform="translate(71,91)">
                <polygon points="5,0 6.18,3.63 10,4.09 7,6.91 7.9,10.68 5,8.8 2.1,10.68 3,6.91 0,4.09 3.82,3.63" fill="#ffd700" stroke="#bfa340" strokeWidth="0.5" />
              </g>
            </g>
          </svg>
        </div>
      </div>
      {/* Carousel hero background with fade and indicator dots */}
      <motion.div
        className="absolute inset-0 z-0 overflow-hidden"
        style={{ scale: imageScale }}
        aria-hidden="true"
      >
        <CarouselBackground />
        {/* Overlay for darkening effect */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 to-black/80 pointer-events-none" />
      </motion.div>

      <SparkParticles />

      <div className="relative z-10 container mx-auto w-full px-4 pt-8 pb-10 md:pt-16 md:pb-20 flex flex-col items-start">
        <motion.div
           initial={{ opacity: 0, y: 20 }}
           animate={{ opacity: 1, y: 0 }}
           transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
           className="mb-6 max-w-3xl text-left relative"
         >
          <div className="flex flex-col items-start justify-start pt-0 mb-6">
  <h1 className="text-4xl md:text-6xl font-extrabold drop-shadow-xl text-left leading-tight md:leading-tight">
    {dictionary.title || "Kanzleiname Muster & Partner"}
  </h1>
  <span className="block text-lg md:text-2xl font-semibold text-gray-200 mt-2 mb-6 text-left drop-shadow-lg">
    {dictionary.subtitle || "Kompetente Rechtsberatung für Privatpersonen und Unternehmen"}
  </span>
</div>
<p className="max-w-xl text-lg md:text-xl font-medium mt-2 mb-8 text-neutral-300 leading-relaxed text-left">
  {dictionary.description || "Rely on our experienced legal team for comprehensive advice and effective representation. Whether you need support in family, business, or criminal law, we are at your side."}
</p>
        </motion.div>
        {/* CTA Buttons */}
        <motion.div
           initial={{ opacity: 0, y: 20 }}
           animate={{ opacity: 1, y: 0 }}
           transition={{ duration: 0.6, delay: 1.6 }}
           className="flex flex-col sm:flex-row flex-wrap items-start gap-4 mt-8 justify-start w-full"
         >
          {/* Contact Us Button - Primary Action */}
          <motion.a
            href="#contact"
            className="w-full max-w-xs md:w-auto bg-gradient-to-r from-blue-600 to-teal-500 hover:from-blue-700 hover:to-teal-600 text-white px-8 py-4 md:px-8 md:py-4 rounded-xl font-semibold flex items-center justify-center gap-3 whitespace-nowrap shadow-xl shadow-indigo-800/30 transition-all duration-300"
            whileHover={{ scale: 1.05, boxShadow: "0px 0px 20px rgba(217, 70, 239, 0.5)" }}
            whileTap={{ scale: 0.97 }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-white group-hover:rotate-12 transition-transform duration-300"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
              />
            </svg>
            <span className="relative z-10">{dictionary.cta?.primary}</span>
          </motion.a>
          {/* Book Consultation Button - Secondary Action */}
          <motion.a
            href="#about"
            className="w-full max-w-xs md:w-auto bg-white/10 text-white border border-white/70 px-8 py-4 md:px-10 md:py-4 rounded-xl font-semibold flex items-center justify-center gap-3 backdrop-blur-sm"
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.98 }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <span className="relative z-10">{dictionary.cta?.secondary}</span>
          </motion.a>
        </motion.div>

        {/* Trust Indicators Row */}
        <motion.div
           initial={{ opacity: 0, y: 20 }}
           animate={{ opacity: 1, y: 0 }}
           transition={{ duration: 0.6, delay: 1.7 }}
           className="mt-8 flex flex-col sm:flex-row flex-wrap justify-start gap-x-3 gap-y-3 w-full items-start"
         >
          {/* TrustPilot Rating */}
          <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md p-2 rounded-lg border border-white/10 w-full max-w-xs min-h-[64px] md:w-auto md:min-w-[320px] md:gap-3 md:p-3">
            <svg
              className="w-24 h-8"
              viewBox="0 0 126 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M27.4 0H34.7V24H27.4V0Z" fill="#00B67A" />
              <path d="M41.3 0H48.6L48.6 24H41.3V0Z" fill="#00B67A" />
              <path d="M13.7 0H21V24H13.7V0Z" fill="#00B67A" />
              <path d="M0 0H7.3V24H0V0Z" fill="#00B67A" />
              <path d="M55.2 0L62.5 0V24H55.2V0Z" fill="#00B67A" />
              <path
                d="M7.3 0L0 24H6.1L7.3 20.1H13.7L14.9 24H21L13.7 0H7.3ZM8.5 15L10.5 8.2L12.5 15H8.5Z"
                fill="#00B67A"
              />
              <path
                d="M75.8 15.8C74.9 16.7 73.7 17.1 72.2 17.1C69.5 17.1 67.4 15 67.4 12.2C67.4 9.4 69.5 7.3 72.2 7.3C73.7 7.3 74.9 7.7 75.8 8.6L80.1 4.3C78 2.2 75.3 1 72.2 1C66 1 61 5.9 61 12.2C61 18.5 66 23.4 72.2 23.4C75.3 23.4 78 22.2 80.1 20.1L75.8 15.8Z"
                fill="#00B67A"
              />
              <path
                d="M109.3 0L103.5 9.8L97.7 0H90.5L100.1 15.8V24H106.9V15.8L116.5 0H109.3Z"
                fill="#00B67A"
              />
              <path
                d="M126 18.2H120.2C120.2 18.2 117.5 12.2 117.3 11.8L117.1 11.4C117.1 11.4 117.1 18.2 117.1 18.2H111.3V1H117.1C117.1 1 119.8 7 120 7.4L120.2 7.8C120.2 7.8 120.2 1 120.2 1H126V18.2Z"
                fill="#00B67A"
              />
              <path
                d="M86.9 18.2H81.1V1H86.9C92.3 1 96.7 5.4 96.7 9.6C96.7 13.8 92.3 18.2 86.9 18.2ZM86.9 6.8H87.1V12.4H86.9C85.3 12.4 84 11.1 84 9.6C84 8.1 85.3 6.8 86.9 6.8Z"
                fill="#00B67A"
              />
            </svg>
            <div className="text-white text-sm">
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg
                    key={star}
                    className="w-5 h-5 text-green-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <div className="font-medium">Excellent - 500+ reviews</div>
            </div>
          </div>

          {/* Google Reviews */}
          <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md p-2 rounded-lg border border-white/10 w-full max-w-xs min-h-[64px] md:w-auto md:min-w-[320px] md:gap-3 md:p-3">
            <svg
              className="w-8 h-8"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M22.5 12.2329C22.5 11.3709 22.4296 10.5089 22.28 9.67688H12V13.7549H17.92C17.64 15.0049 16.8904 16.0969 15.7904 16.8329V19.4449H19.28C21.36 17.5329 22.5 15.1009 22.5 12.2329Z"
                fill="#4285F4"
              />
              <path
                d="M12 23C14.97 23 17.46 22.02 19.28 19.4449L15.79 16.8329C14.83 17.4889 13.56 17.8689 12 17.8689C9.13004 17.8689 6.72004 15.9569 5.82004 13.3H2.21004V15.9889C4.02004 20.1449 7.73004 23 12 23Z"
                fill="#34A853"
              />
              <path
                d="M5.82003 13.2999C5.58003 12.6439 5.44003 11.9319 5.44003 11.1999C5.44003 10.4679 5.58003 9.75588 5.82003 9.09988V6.41089H2.21003C1.44003 7.85189 1.00003 9.47188 1.00003 11.1999C1.00003 12.9279 1.44003 14.5479 2.21003 15.9889L5.82003 13.2999Z"
                fill="#FBBC05"
              />
              <path
                d="M12 5.52998C13.62 5.52998 15.06 6.08998 16.21 7.18998L19.28 4.11998C17.46 2.41998 14.97 1.33998 12 1.33998C7.73004 1.33998 4.02004 4.19498 2.21004 8.35098L5.82004 11.04C6.72004 8.38298 9.13004 5.52998 12 5.52998Z"
                fill="#EA4335"
              />
            </svg>
            <div className="text-white text-sm">
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg
                    key={star}
                    className="w-5 h-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <div className="font-medium">4.9 - 350+ Google reviews</div>
            </div>
          </div>        
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection;
