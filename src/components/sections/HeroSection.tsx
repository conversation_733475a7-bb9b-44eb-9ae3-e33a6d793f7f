'use client'

import React, { useRef, useEffect, useState } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import Image from "next/image";
import { type Dictionary } from "@/lib/dictionary";
import { ArrowRight, Mail, Calendar, Star, Shield, Award } from "lucide-react";

interface HeroSectionProps {
  dictionary: Dictionary["hero"];
}

// Spark particles component that responds to scrolling
const SparkParticles = () => {
  // Reference to the container for scroll tracking
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"],
  });

  // Generate random sparks with different properties
  const generateSparks = (count: number) => {
    return Array.from({ length: count }).map((_, i) => {
      // Random properties for each spark
      const size = Math.random() * 3 + 1;
      const initialX = Math.random() * 100;
      const initialY = Math.random() * 100;
      const speed = Math.random() * 15 + 5;
      const delay = Math.random() * 2;
      const duration = Math.random() * 10 + 10;
      const opacity = Math.random() * 0.5 + 0.2;

      // Create parallax effect based on position
      const parallaxStrength = Math.random() * 100 + 50;
      const xParallax = useTransform(
        scrollYProgress,
        [0, 1],
        [initialX, initialX + (Math.random() * 2 - 1) * parallaxStrength]
      );
      const yParallax = useTransform(
        scrollYProgress,
        [0, 1],
        [initialY, initialY + (Math.random() * 2 - 1) * parallaxStrength]
      );

      return {
        id: i,
        size,
        initialX,
        initialY,
        speed,
        delay,
        duration,
        opacity,
        xParallax,
        yParallax,
      };
    });
  };

  // Create small and large sparks with different properties
  const smallSparks = generateSparks(40); // More small sparks for a richer effect
  const largeSparks = generateSparks(15); // Fewer large sparks for accent

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 overflow-hidden pointer-events-none"
    >
      {smallSparks.map((spark) => (
        <motion.div
          key={`small-${spark.id}`}
          className="absolute rounded-full bg-white"
          style={{
            width: spark.size,
            height: spark.size,
            left: `${spark.initialX}%`,
            top: `${spark.initialY}%`,
            x: spark.xParallax,
            y: spark.yParallax,
            opacity: spark.opacity,
          }}
          animate={{
            y: [0, -spark.speed * 4],
            opacity: [0, spark.opacity, 0],
          }}
          transition={{
            duration: spark.duration,
            delay: spark.delay,
            repeat: Infinity,
            ease: "linear",
          }}
        />
      ))}

      {largeSparks.map((spark) => (
        <motion.div
          key={`large-${spark.id}`}
          className="absolute rounded-full bg-gradient-to-r from-blue-400/20 to-violet-400/20"
          style={{
            width: spark.size * 8,
            height: spark.size * 8,
            left: `${spark.initialX}%`,
            top: `${spark.initialY}%`,
            x: spark.xParallax,
            y: spark.yParallax,
            opacity: spark.opacity * 0.75,
            filter: `blur(${spark.size * 1.5}px)`,
          }}
          animate={{
            y: [0, -spark.speed * 8],
            x: [(Math.random() - 0.5) * 35, (Math.random() - 0.5) * 55],
            opacity: [0, spark.opacity, 0],
            scale: [0.2, 1, 0.8, 0.2],
          }}
          transition={{
            duration: spark.duration * 1.5,
            delay: spark.delay,
            repeat: Infinity,
            ease: "linear",
          }}
        />
      ))}
    </div>
  );
};
// Carousel background for hero section
const heroImages = [
  "/images/lawyers/hero_bg_0.jpg",
  "/images/lawyers/hero_bg_1.jpg",
  "/images/lawyers/hero_bg_2.jpg",
];

const CarouselBackground = () => {
  const [current, setCurrent] = useState(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    timeoutRef.current = setTimeout(() => {
      setCurrent((prev) => (prev + 1) % heroImages.length);
    }, 6000);
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [current]);

  return (
    <div className="absolute inset-0 w-full h-full">
      {heroImages.map((src, idx) => (
        <motion.div
          key={src}
          initial={{ opacity: 0 }}
          animate={{ opacity: idx === current ? 1 : 0 }}
          transition={{ duration: 1.2, ease: "easeInOut" }}
          className="absolute inset-0 w-full h-full"
          aria-hidden={idx !== current}
          style={{ pointerEvents: "none" }}
        >
          <Image
            src={src}
            alt="Kanzlei Hintergrund"
            fill
            priority={idx === 0}
            className="object-cover object-center w-full h-full"
            sizes="100vw"
            quality={90}
          />
        </motion.div>
      ))}
      {/* Indicator dots */}
      <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-2 z-10">
        {heroImages.map((_, idx) => (
          <span
            key={idx}
            className={`w-3 h-3 rounded-full bg-white transition-all duration-300 ${
              current === idx ? "opacity-90 scale-110" : "opacity-50 scale-90"
            }`}
            aria-label={current === idx ? "Aktives Hintergrundbild" : undefined}
            aria-current={current === idx ? "true" : undefined}
          />
        ))}
      </div>
    </div>
  );
};

export const HeroSection = ({ dictionary }: HeroSectionProps) => {
  const sectionRef = useRef<HTMLElement>(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"],
  });

  // Create a transform scale for the background image that increases as you scroll
  const imageScale = useTransform(scrollYProgress, [0, 1], [1, 1.2]);

  return (
    <section
      ref={sectionRef}
      id="hero"
      className="relative w-full text-white min-h-screen flex flex-col items-center justify-center overflow-hidden"
    >
      {/* WCAG Badge - Professional positioning */}
      <div
        className="absolute top-24 right-6 z-30 focus:outline-none focus:ring-4 focus:ring-blue-300/50 w-20 h-20 md:w-24 md:h-24 md:top-28 md:right-8 transition-all opacity-80 hover:opacity-100"
        aria-label="WCAG 2.0 Konform"
        tabIndex={0}
      >
        {/* Professional WCAG Badge */}
        <div className="relative w-20 h-20 md:w-24 md:h-24 flex items-center justify-center select-none">
          <svg
            viewBox="0 0 112 112"
            width="100%"
            height="100%"
            className="drop-shadow-lg"
            aria-label="WCAG 2.0 Konform Siegel"
            role="img"
          >
            {/* Goldener Kreis mit Glanz */}
            <defs>
              <radialGradient id="gold-gradient" cx="50%" cy="50%" r="50%">
                <stop offset="0%" stopColor="#fffbe8" />
                <stop offset="60%" stopColor="#ffe082" />
                <stop offset="100%" stopColor="#bfa340" />
              </radialGradient>
              <linearGradient id="shine" x1="0" y1="0" x2="1" y2="1">
                <stop offset="0%" stopColor="#fff" stopOpacity="0.7" />
                <stop offset="60%" stopColor="#fff" stopOpacity="0" />
              </linearGradient>
            </defs>
            <circle
              cx="56"
              cy="56"
              r="50"
              fill="url(#gold-gradient)"
              stroke="#e6c558"
              strokeWidth="4"
            />
            {/* Glanz-Effekt */}
            <ellipse cx="45" cy="38" rx="28" ry="10" fill="url(#shine)" />
            {/* Häkchen im Kreis */}
            <circle cx="56" cy="56" r="22" fill="#fff" opacity="0.15" />

            {/* Text */}
            <text
              x="56"
              y="50"
              textAnchor="middle"
              fontSize="17"
              fontWeight="bold"
              fill="#6d4c00"
              fontFamily="'Inter', Arial, sans-serif"
              letterSpacing="1"
            >
              WCAG
            </text>
            <text
              x="56"
              y="67"
              textAnchor="middle"
              fontSize="15"
              fontWeight="bold"
              fill="#6d4c00"
              fontFamily="'Inter', Arial, sans-serif"
            >
              2.0
            </text>
            <text
              x="56"
              y="86"
              textAnchor="middle"
              fontSize="10"
              fill="#7b6a3d"
              fontFamily="'Inter', Arial, sans-serif"
              letterSpacing="0.5"
            >
              KONFORM
            </text>
            {/* Sterne */}
            <g>
              <g transform="translate(41,91)">
                <polygon
                  points="5,0 6.18,3.63 10,4.09 7,6.91 7.9,10.68 5,8.8 2.1,10.68 3,6.91 0,4.09 3.82,3.63"
                  fill="#ffd700"
                  stroke="#bfa340"
                  strokeWidth="0.5"
                />
              </g>
              <g transform="translate(56,93)">
                <polygon
                  points="5,0 6.18,3.63 10,4.09 7,6.91 7.9,10.68 5,8.8 2.1,10.68 3,6.91 0,4.09 3.82,3.63"
                  fill="#ffd700"
                  stroke="#bfa340"
                  strokeWidth="0.5"
                />
              </g>
              <g transform="translate(71,91)">
                <polygon
                  points="5,0 6.18,3.63 10,4.09 7,6.91 7.9,10.68 5,8.8 2.1,10.68 3,6.91 0,4.09 3.82,3.63"
                  fill="#ffd700"
                  stroke="#bfa340"
                  strokeWidth="0.5"
                />
              </g>
            </g>
          </svg>
        </div>
      </div>
      {/* Carousel hero background with fade and indicator dots */}
      <motion.div
        className="absolute inset-0 z-0 overflow-hidden"
        style={{ scale: imageScale }}
        aria-hidden="true"
      >
        <CarouselBackground />
        {/* Overlay for darkening effect */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 to-black/80 pointer-events-none" />
      </motion.div>

      <SparkParticles />

      <div className="relative z-10 container mx-auto w-full px-6 pt-12 pb-16 md:pt-20 md:pb-24 flex flex-col items-start max-w-7xl">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
          className="mb-8 max-w-4xl text-left relative"
        >
          {/* Professional Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-6"
          >
            <Shield className="w-4 h-4 text-blue-300" />
            <span className="text-sm font-medium text-white/90">
              Seit 2008 • Über 2000 erfolgreiche Fälle
            </span>
          </motion.div>

          <div className="flex flex-col items-start justify-start mb-8">
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-white leading-[0.9] md:leading-[0.85] tracking-tight mb-4">
              {dictionary.title || "Kanzlei Muster & Partner"}
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-teal-400 rounded-full mb-6"></div>
            <span className="block text-xl md:text-2xl lg:text-3xl font-light text-white/90 leading-relaxed max-w-3xl">
              {dictionary.subtitle ||
                "Kompetente Rechtsberatung für Privatpersonen und Unternehmen"}
            </span>
          </div>

          <p className="max-w-2xl text-lg md:text-xl font-normal text-white/80 leading-relaxed mb-8">
            {dictionary.description ||
              "Vertrauen Sie auf unser erfahrenes Anwaltsteam für umfassende Beratung und effektive Vertretung. Ob Familien-, Wirtschafts- oder Strafrecht – wir stehen an Ihrer Seite."}
          </p>
        </motion.div>
        {/* CTA Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="flex flex-col sm:flex-row items-start gap-4 mb-12 w-full"
        >
          {/* Primary CTA - Contact */}
          <motion.a
            href="#contact"
            className="group relative overflow-hidden bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 hover:from-blue-700 hover:via-blue-800 hover:to-blue-900 text-white px-8 py-4 rounded-2xl font-semibold flex items-center gap-3 shadow-2xl shadow-blue-900/30 transition-all duration-300 border border-blue-500/20"
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <Mail className="w-5 h-5 relative z-10" />
            <span className="relative z-10 text-lg">
              {dictionary.cta?.primary || "Kostenlose Beratung"}
            </span>
            <ArrowRight className="w-5 h-5 relative z-10 group-hover:translate-x-1 transition-transform duration-300" />
          </motion.a>

          {/* Secondary CTA - Book Consultation */}
          <motion.a
            href="#booking"
            className="group bg-white/5 hover:bg-white/10 text-white border-2 border-white/30 hover:border-white/50 px-8 py-4 rounded-2xl font-semibold flex items-center gap-3 backdrop-blur-sm transition-all duration-300"
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
          >
            <Calendar className="w-5 h-5" />
            <span className="text-lg">
              {dictionary.cta?.secondary || "Termin buchen"}
            </span>
          </motion.a>
        </motion.div>

        {/* Trust Indicators Row */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="flex flex-col sm:flex-row gap-4 w-full max-w-4xl"
        >
          {/* Professional Stats */}
          <div className="flex items-center gap-3 bg-white/5 backdrop-blur-md p-4 rounded-2xl border border-white/10 flex-1">
            <div className="flex items-center gap-2">
              <Award className="w-6 h-6 text-yellow-400" />
              <div>
                <div className="flex items-center gap-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className="w-4 h-4 text-yellow-400 fill-current"
                    />
                  ))}
                </div>
                <p className="text-sm text-white/80 font-medium">
                  4.9/5 • 500+ Bewertungen
                </p>
              </div>
            </div>
          </div>

          {/* Experience Badge */}
          <div className="flex items-center gap-3 bg-white/5 backdrop-blur-md p-4 rounded-2xl border border-white/10 flex-1">
            <Shield className="w-6 h-6 text-blue-300" />
            <div>
              <p className="text-white font-semibold">15+ Jahre Erfahrung</p>
              <p className="text-sm text-white/70">
                Über 2000 erfolgreiche Fälle
              </p>
            </div>
          </div>

          {/* Success Rate */}
          <div className="flex items-center gap-3 bg-white/5 backdrop-blur-md p-4 rounded-2xl border border-white/10 flex-1">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center">
                <span className="text-green-400 font-bold text-sm">98%</span>
              </div>
              <div>
                <p className="text-white font-semibold">Erfolgsquote</p>
                <p className="text-sm text-white/70">Zufriedene Mandanten</p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection;
