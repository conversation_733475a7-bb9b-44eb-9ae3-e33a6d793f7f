'use client'

import { Section } from "@/components/ui/Section";
import type { Dictionary } from "@/lib/dictionary";
import Image from "next/image";
import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Star, ChevronLeft, ChevronRight, Quote, Scale, MessageSquare, Send } from "lucide-react";
import { cn } from "@/lib/utils";
import { useInView } from "react-intersection-observer";

interface TestimonialsSectionProps {
  dictionary: Dictionary;
}

interface Testimonial {
  quote: string;
  name: string;
  designation: string;
  src: string;
  caseType: string;
  rating: number;
  date: string;
  location: string;
}

interface TestimonialsDictionary {
  title: string;
  subtitle: string;
  description?: string;
  readMore: string;
  readLess: string;
  formTitle: string;
}

export const TestimonialsSection = ({
  dictionary,
}: TestimonialsSectionProps) => {
  // Function to get testimonials based on language
  const getLocalizedTestimonials = (): Testimonial[] => {
    // First check if we have a specific locale identifier
    if (dictionary.testimonials?.familyLaw === "Family Law") {
      // English testimonials
      return [
        {
          quote:
            "Dr<PERSON> guided us through a complex divorce process with empathy and the highest professionalism. Thanks to his expertise, we were able to reach a fair agreement.",
          name: "Mark & <PERSON> Brendel",
          designation: "Family Law",
          src: "/images/lawyers/testimonial1.jpg",
          caseType: "Divorce Proceedings",
          rating: 5,
          date: "June 2023",
          location: "Munich",
        },
        {
          quote:
            "After my unjustified dismissal, Dr. Schmidt stood by me and successfully enforced an appropriate severance package. Her strategic approach and negotiation skills were impressive.",
          name: "Anna Miller",
          designation: "Employment Law",
          src: "/images/lawyers/testimonial2.jpg",
          caseType: "Wrongful Termination",
          rating: 5,
          date: "March 2023",
          location: "Berlin",
        },
        {
          quote:
            "Mr. Weber represented me in a difficult criminal case and achieved an acquittal through his meticulous preparation and persuasive argumentation. His calm and confident manner gave me great support during this difficult time.",
          name: "Henry Baker",
          designation: "Criminal Law",
          src: "/images/lawyers/testimonial3.jpg",
          caseType: "Criminal Defense",
          rating: 5,
          date: "May 2023",
          location: "Hamburg",
        },
        {
          quote:
            "After my traffic accident, Ms. Bauer was an invaluable help in enforcing my claims. She stood up against the insurance company and helped me get fair compensation.",
          name: "Sophie Schneider",
          designation: "Traffic Law",
          src: "/images/lawyers/testimonial4.jpg",
          caseType: "Accident Settlement",
          rating: 5,
          date: "April 2023",
          location: "Frankfurt",
        },
        {
          quote:
            "Mr. Hoffman created and reviewed several complex contracts for our company. His precise work and understanding of our business requirements saved us from costly mistakes.",
          name: "Thomas Smith",
          designation: "Contract Law",
          src: "/images/lawyers/testimonial5.jpg",
          caseType: "Contract Design",
          rating: 4,
          date: "July 2023",
          location: "Cologne",
        },
      ];
    } else if (dictionary.testimonials?.familyLaw === "Семейное право") {
      // Russian testimonials
      return [
        {
          quote:
            "Д-р Миллер сопровождал нас в сложном бракоразводном процессе, проявляя эмпатию и высочайший профессионализм. Благодаря его экспертизе мы смогли достичь справедливого соглашения.",
          name: "Марк и Иоанна Брендель",
          designation: "Семейное право",
          src: "/images/lawyers/testimonial1.jpg",
          caseType: "Бракоразводный процесс",
          rating: 5,
          date: "Июнь 2023",
          location: "Мюнхен",
        },
        {
          quote:
            "После неоправданного увольнения доктор Шмидт была на моей стороне и успешно добилась соответствующего выходного пособия. Ее стратегический подход и навыки ведения переговоров были впечатляющими.",
          name: "Анна Миллер",
          designation: "Трудовое право",
          src: "/images/lawyers/testimonial2.jpg",
          caseType: "Защита от незаконного увольнения",
          rating: 5,
          date: "Март 2023",
          location: "Берлин",
        },
        {
          quote:
            "Господин Вебер представлял меня в сложном уголовном деле и добился оправдания благодаря своей тщательной подготовке и убедительной аргументации. Его спокойная и уверенная манера дала мне большую поддержку в это трудное время.",
          name: "Генрих Бауэр",
          designation: "Уголовное право",
          src: "/images/lawyers/testimonial3.jpg",
          caseType: "Уголовная защита",
          rating: 5,
          date: "Май 2023",
          location: "Гамбург",
        },
        {
          quote:
            "После моей автомобильной аварии г-жа Бауэр оказала неоценимую помощь в отстаивании моих требований. Она противостояла страховой компании и помогла мне получить справедливую компенсацию.",
          name: "София Шнайдер",
          designation: "Транспортное право",
          src: "/images/lawyers/testimonial4.jpg",
          caseType: "Урегулирование страхового случая",
          rating: 5,
          date: "Апрель 2023",
          location: "Франкфурт",
        },
        {
          quote:
            "Господин Хофман создал и проверил несколько сложных контрактов для нашей компании. Его точная работа и понимание требований нашего бизнеса уберегли нас от дорогостоящих ошибок.",
          name: "Томас Шмидт",
          designation: "Договорное право",
          src: "/images/lawyers/testimonial5.jpg",
          caseType: "Составление контрактов",
          rating: 4,
          date: "Июль 2023",
          location: "Кёльн",
        },
      ];
    } else if (dictionary.testimonials?.familyLaw === "Aile Hukuku") {
      // Turkish testimonials
      return [
        {
          quote:
            "Dr. Miller, karmaşık bir boşanma sürecinde bize empati ve en yüksek profesyonellikle rehberlik etti. Uzmanlığı sayesinde adil bir anlaşmaya varabildik.",
          name: "Mark ve Joanna Brendel",
          designation: "Aile Hukuku",
          src: "/images/lawyers/testimonial1.jpg",
          caseType: "Boşanma Davası",
          rating: 5,
          date: "Haziran 2023",
          location: "Münih",
        },
        {
          quote:
            "Haksız işten çıkarılmamdan sonra Dr. Schmidt yanımda durdu ve uygun bir tazminat paketini başarıyla uyguladı. Stratejik yaklaşımı ve müzakere becerileri etkileyiciydi.",
          name: "Anna Miller",
          designation: "İş Hukuku",
          src: "/images/lawyers/testimonial2.jpg",
          caseType: "İşten Çıkarılmaya Karşı Koruma",
          rating: 5,
          date: "Mart 2023",
          location: "Berlin",
        },
        {
          quote:
            "Bay Weber beni zor bir ceza davasında temsil etti ve titiz hazırlığı ve ikna edici argümanlarıyla beraat kazandı. Sakin ve kendinden emin tavrı, bu zor zamanda bana büyük destek verdi.",
          name: "Heinrich Bauer",
          designation: "Ceza Hukuku",
          src: "/images/lawyers/testimonial3.jpg",
          caseType: "Ceza Savunması",
          rating: 5,
          date: "Mayıs 2023",
          location: "Hamburg",
        },
        {
          quote:
            "Trafik kazamdan sonra Bayan Bauer, taleplerimizi uygulamada paha biçilmez bir yardım oldu. Sigorta şirketine karşı durdu ve adil bir tazminat almama yardımcı oldu.",
          name: "Sofia Schneider",
          designation: "Trafik Hukuku",
          src: "/images/lawyers/testimonial4.jpg",
          caseType: "Kaza Uzlaşması",
          rating: 5,
          date: "Nisan 2023",
          location: "Frankfurt",
        },
        {
          quote:
            "Bay Hoffman şirketimiz için birkaç karmaşık sözleşme oluşturdu ve inceledi. Titiz çalışması ve işimizin gereksinimlerini anlaması bizi maliyetli hatalardan korudu.",
          name: "Thomas Schmidt",
          designation: "Sözleşme Hukuku",
          src: "/images/lawyers/testimonial5.jpg",
          caseType: "Sözleşme Tasarımı",
          rating: 4,
          date: "Temmuz 2023",
          location: "Köln",
        },
      ];
    } else if (dictionary.testimonials?.familyLaw === "قانون الأسرة") {
      // Arabic testimonials
      return [
        {
          quote:
            "قاد د. ميلر عملية طلاق معقدة بتعاطف ومهنية عالية. بفضل خبرته، تمكنا من التوصل إلى اتفاق عادل.",
          name: "مارك وجوانا برينديل",
          designation: "قانون الأسرة",
          src: "/images/lawyers/testimonial1.jpg",
          caseType: "إجراءات الطلاق",
          rating: 5,
          date: "يونيو 2023",
          location: "ميونخ",
        },
        {
          quote:
            "بعد فصلي غير المبرر، وقفت د. شميدت بجانبي ونجحت في تنفيذ حزمة تعويضات مناسبة. كان نهجها الاستراتيجي ومهارات التفاوض مثيرة للإعجاب.",
          name: "آنا ميلر",
          designation: "قانون العمل",
          src: "/images/lawyers/testimonial2.jpg",
          caseType: "الحماية من الفصل",
          rating: 5,
          date: "مارس 2023",
          location: "برلين",
        },
        {
          quote:
            "مثلني السيد ويبر في قضية جنائية صعبة وحقق البراءة من خلال إعداده الدقيق وحججه المقنعة. أسلوبه الهادئ والواثق منحني دعمًا كبيرًا خلال هذا الوقت العصيب.",
          name: "هاينريش باور",
          designation: "القانون الجنائي",
          src: "/images/lawyers/testimonial3.jpg",
          caseType: "الدفاع الجنائي",
          rating: 5,
          date: "مايو 2023",
          location: "هامبورغ",
        },
        {
          quote:
            "بعد حادث المرور الخاص بي، كانت السيدة باور مساعدة لا تقدر بثمن في إنفاذ مطالباتي. وقفت ضد شركة التأمين وساعدتني في الحصول على تعويض عادل.",
          name: "صوفيا شنايدر",
          designation: "قانون المرور",
          src: "/images/lawyers/testimonial4.jpg",
          caseType: "تسوية الحوادث",
          rating: 5,
          date: "أبريل 2023",
          location: "فرانكفورت",
        },
        {
          quote:
            "أنشأ السيد هوفمان وراجع العديد من العقود المعقدة لشركتنا. عمله الدقيق وفهمه لمتطلبات أعمالنا أنقذنا من أخطاء مكلفة.",
          name: "توماس شميدت",
          designation: "قانون العقود",
          src: "/images/lawyers/testimonial5.jpg",
          caseType: "تصميم العقود",
          rating: 4,
          date: "يوليو 2023",
          location: "كولونيا",
        },
      ];
    } else {
      // Default to German testimonials
      return [
        {
          quote:
            "Dr. Müller hat uns durch einen komplexen Scheidungsprozess begleitet und dabei stets Einfühlungsvermögen und höchste Professionalität bewiesen. Dank seiner Expertise konnten wir eine faire Einigung erzielen.",
          name: "Markus & Johanna Brendel",
          designation: "Familienrecht",
          src: "/images/lawyers/testimonial1.jpg",
          caseType: "Scheidungsverfahren",
          rating: 5,
          date: "Juni 2023",
          location: "München",
        },
        {
          quote:
            "Nach meiner ungerechtfertigten Kündigung stand Frau Dr. Schmidt mir zur Seite und hat erfolgreich eine angemessene Abfindung durchgesetzt. Ihre strategische Vorgehensweise und ihr Verhandlungsgeschick waren beeindruckend.",
          name: "Anna Müller",
          designation: "Arbeitsrecht",
          src: "/images/lawyers/testimonial2.jpg",
          caseType: "Kündigungsschutzklage",
          rating: 5,
          date: "März 2023",
          location: "Berlin",
        },
        {
          quote:
            "Herr Weber hat mich in einem schwierigen Strafverfahren vertreten und durch seine akribische Vorbereitung und überzeugende Argumentation einen Freispruch erwirkt. Seine ruhige und zuversichtliche Art gab mir in dieser schweren Zeit viel Halt.",
          name: "Heinrich Bauer",
          designation: "Strafrecht",
          src: "/images/lawyers/testimonial3.jpg",
          caseType: "Strafverteidigung",
          rating: 5,
          date: "Mai 2023",
          location: "Hamburg",
        },
        {
          quote:
            "Nach meinem Verkehrsunfall war Frau Bauer eine unschätzbare Hilfe bei der Durchsetzung meiner Ansprüche. Sie hat sich gegen die Versicherung durchgesetzt und mir zu einer gerechten Entschädigung verholfen.",
          name: "Sofia Schneider",
          designation: "Verkehrsrecht",
          src: "/images/lawyers/testimonial4.jpg",
          caseType: "Unfallregulierung",
          rating: 5,
          date: "April 2023",
          location: "Frankfurt",
        },
        {
          quote:
            "Herr Hoffmann hat für unser Unternehmen mehrere komplexe Verträge erstellt und geprüft. Seine präzise Arbeitsweise und sein Verständnis für unsere Geschäftsanforderungen haben uns vor kostspieligen Fehlern bewahrt.",
          name: "Thomas Schmidt",
          designation: "Vertragsrecht",
          src: "/images/lawyers/testimonial5.jpg",
          caseType: "Vertragsgestaltung",
          rating: 4,
          date: "Juli 2023",
          location: "Köln",
        },
      ];
    }
  };

  // Get testimonials based on language
  const testimonials: Testimonial[] = getLocalizedTestimonials();

  // State for carousel
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);
  const { ref: sectionRef, inView } = useInView({
    triggerOnce: false,
    threshold: 0.1,
  });

  // Function to go to next slide
  const nextSlide = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prevIndex) =>
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
    setTimeout(() => setIsAnimating(false), 500);
  };

  // Function to go to previous slide
  const prevSlide = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
    setTimeout(() => setIsAnimating(false), 500);
  };

  // Auto-rotation effect
  useEffect(() => {
    if (inView) {
      autoPlayRef.current = setInterval(() => {
        nextSlide();
      }, 6000); // 6 seconds rotation
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [inView, currentIndex, isAnimating]);

  // Function to render star rating
  const renderStars = (rating: number) => {
    return Array(5)
      .fill(0)
      .map((_, i) => (
        <Star
          key={i}
          className={cn(
            "w-4 h-4",
            i < rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
          )}
        />
      ));
  };

  return (
    <Section
      className="py-16 sm:py-20 md:py-28 bg-gradient-to-b from-gray-50/50 to-white dark:from-gray-900 dark:to-gray-900/95 relative overflow-hidden"
      id="testimonials"
      aria-labelledby="testimonials-heading"
    >
      {/* Background elements */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-blue-50/20 via-transparent to-transparent dark:from-blue-900/10"></div>
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-200/50 dark:via-blue-700/30 to-transparent"></div>

      <div className="container mx-auto px-6 max-w-7xl relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6 }}
            className="mb-6"
          >
            <span className="inline-block px-6 py-3 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium">
              Kundenstimmen
            </span>
          </motion.div>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-5xl md:text-6xl font-bold mb-6 text-gray-900 dark:text-white leading-tight"
          >
            {dictionary.testimonials?.title || "Was unsere Mandanten sagen"}
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed"
          >
            {dictionary.testimonials?.description ||
              "Vertrauen Sie auf die Erfahrungen unserer zufriedenen Mandanten und erfahren Sie, wie wir ihnen geholfen haben."}
          </motion.p>
        </div>

        {/* Testimonial Carousel */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative max-w-5xl mx-auto mb-20"
        >
          {/* Testimonial Card */}
          <div className="relative overflow-hidden">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
                className="bg-white dark:bg-gray-800/50 backdrop-blur-sm rounded-3xl p-8 md:p-12 shadow-xl border border-gray-200/50 dark:border-gray-700/50 relative overflow-hidden"
              >
                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-full -translate-y-16 translate-x-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-full translate-y-12 -translate-x-12"></div>

                {/* Quote Icon */}
                <div className="absolute top-8 right-8 text-blue-100 dark:text-blue-900/30">
                  <Quote className="w-16 h-16 opacity-30" />
                </div>

                <div className="relative z-10">
                  {/* Star Rating */}
                  <div className="flex justify-center mb-6">
                    {renderStars(testimonials[currentIndex].rating)}
                  </div>

                  {/* Quote */}
                  <blockquote className="text-center mb-8">
                    <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 leading-relaxed font-medium italic">
                      "{testimonials[currentIndex].quote}"
                    </p>
                  </blockquote>

                  {/* Client Info */}
                  <div className="flex flex-col items-center text-center">
                    {/* Client Image */}
                    <div className="relative w-16 h-16 mb-4">
                      <div className="w-full h-full rounded-full overflow-hidden border-3 border-blue-100 dark:border-blue-800 shadow-lg">
                        <Image
                          src={testimonials[currentIndex].src}
                          alt={`Porträt von ${testimonials[currentIndex].name}`}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>

                    <h3 className="font-bold text-gray-900 dark:text-white text-lg mb-1">
                      {testimonials[currentIndex].name}
                    </h3>
                    <div className="text-blue-600 dark:text-blue-400 font-medium mb-2">
                      {testimonials[currentIndex].designation}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {testimonials[currentIndex].caseType} • {testimonials[currentIndex].date} • {testimonials[currentIndex].location}
                    </div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevSlide}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-6 sm:-translate-x-12 bg-white dark:bg-gray-800 w-12 h-12 rounded-full shadow-lg border border-gray-200/50 dark:border-gray-700/50 flex items-center justify-center hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-200 dark:hover:border-blue-700 transition-all duration-300 group"
            aria-label="Previous testimonial"
          >
            <ChevronLeft className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300" />
          </button>
          <button
            onClick={nextSlide}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-6 sm:translate-x-12 bg-white dark:bg-gray-800 w-12 h-12 rounded-full shadow-lg border border-gray-200/50 dark:border-gray-700/50 flex items-center justify-center hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-200 dark:hover:border-blue-700 transition-all duration-300 group"
            aria-label="Next testimonial"
          >
            <ChevronRight className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300" />
          </button>

          {/* Indicators */}
          <div className="flex justify-center mt-8 space-x-3">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? "bg-blue-600 scale-125"
                    : "bg-gray-300 dark:bg-gray-600 hover:bg-blue-400 dark:hover:bg-blue-500 hover:scale-110"
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              ></button>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="text-center bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-3xl p-8 md:p-12 max-w-4xl mx-auto border border-blue-100/50 dark:border-blue-800/30"
        >
          <div className="mb-6">
            <MessageSquare className="w-12 h-12 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
              {dictionary.testimonials?.formTitle || "Werden Sie unser nächster zufriedener Mandant"}
            </h3>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Lassen Sie uns Ihnen dabei helfen, Ihre rechtlichen Herausforderungen erfolgreich zu meistern.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.a
              href="#contact"
              className="inline-flex items-center justify-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <MessageSquare className="w-5 h-5 mr-2" />
              Kostenlose Beratung anfragen
            </motion.a>
            <motion.a
              href="#booking"
              className="inline-flex items-center justify-center px-8 py-4 bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 font-semibold rounded-2xl border-2 border-blue-600 dark:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Termin vereinbaren
            </motion.a>
          </div>
        </div>
      </div>
    </Section>
  );
};
