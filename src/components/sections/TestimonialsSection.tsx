'use client'

import { Section } from "@/components/ui/Section";
import type { Dictionary } from "@/lib/dictionary";
import Image from "next/image";
import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Star, ChevronLeft, ChevronRight, Quote, Scale, MessageSquare, Send } from "lucide-react";
import { cn } from "@/lib/utils";
import { useInView } from "react-intersection-observer";

interface TestimonialsSectionProps {
  dictionary: Dictionary;
}

interface Testimonial {
  quote: string;
  name: string;
  designation: string;
  src: string;
  caseType: string;
  rating: number;
  date: string;
  location: string;
}

interface TestimonialsDictionary {
  title: string;
  subtitle: string;
  description?: string;
  readMore: string;
  readLess: string;
  formTitle: string;
}

export const TestimonialsSection = ({
  dictionary,
}: TestimonialsSectionProps) => {
  // Function to get testimonials based on language
  const getLocalizedTestimonials = (): Testimonial[] => {
    // First check if we have a specific locale identifier
    if (dictionary.testimonials?.familyLaw === "Family Law") {
      // English testimonials
      return [
        {
          quote:
            "Dr<PERSON> guided us through a complex divorce process with empathy and the highest professionalism. Thanks to his expertise, we were able to reach a fair agreement.",
          name: "Mark & <PERSON> Brendel",
          designation: "Family Law",
          src: "/images/lawyers/testimonial1.jpg",
          caseType: "Divorce Proceedings",
          rating: 5,
          date: "June 2023",
          location: "Munich",
        },
        {
          quote:
            "After my unjustified dismissal, Dr. Schmidt stood by me and successfully enforced an appropriate severance package. Her strategic approach and negotiation skills were impressive.",
          name: "Anna Miller",
          designation: "Employment Law",
          src: "/images/lawyers/testimonial2.jpg",
          caseType: "Wrongful Termination",
          rating: 5,
          date: "March 2023",
          location: "Berlin",
        },
        {
          quote:
            "Mr. Weber represented me in a difficult criminal case and achieved an acquittal through his meticulous preparation and persuasive argumentation. His calm and confident manner gave me great support during this difficult time.",
          name: "Henry Baker",
          designation: "Criminal Law",
          src: "/images/lawyers/testimonial3.jpg",
          caseType: "Criminal Defense",
          rating: 5,
          date: "May 2023",
          location: "Hamburg",
        },
        {
          quote:
            "After my traffic accident, Ms. Bauer was an invaluable help in enforcing my claims. She stood up against the insurance company and helped me get fair compensation.",
          name: "Sophie Schneider",
          designation: "Traffic Law",
          src: "/images/lawyers/testimonial4.jpg",
          caseType: "Accident Settlement",
          rating: 5,
          date: "April 2023",
          location: "Frankfurt",
        },
        {
          quote:
            "Mr. Hoffman created and reviewed several complex contracts for our company. His precise work and understanding of our business requirements saved us from costly mistakes.",
          name: "Thomas Smith",
          designation: "Contract Law",
          src: "/images/lawyers/testimonial5.jpg",
          caseType: "Contract Design",
          rating: 4,
          date: "July 2023",
          location: "Cologne",
        },
      ];
    } else if (dictionary.testimonials?.familyLaw === "Семейное право") {
      // Russian testimonials
      return [
        {
          quote:
            "Д-р Миллер сопровождал нас в сложном бракоразводном процессе, проявляя эмпатию и высочайший профессионализм. Благодаря его экспертизе мы смогли достичь справедливого соглашения.",
          name: "Марк и Иоанна Брендель",
          designation: "Семейное право",
          src: "/images/lawyers/testimonial1.jpg",
          caseType: "Бракоразводный процесс",
          rating: 5,
          date: "Июнь 2023",
          location: "Мюнхен",
        },
        {
          quote:
            "После неоправданного увольнения доктор Шмидт была на моей стороне и успешно добилась соответствующего выходного пособия. Ее стратегический подход и навыки ведения переговоров были впечатляющими.",
          name: "Анна Миллер",
          designation: "Трудовое право",
          src: "/images/lawyers/testimonial2.jpg",
          caseType: "Защита от незаконного увольнения",
          rating: 5,
          date: "Март 2023",
          location: "Берлин",
        },
        {
          quote:
            "Господин Вебер представлял меня в сложном уголовном деле и добился оправдания благодаря своей тщательной подготовке и убедительной аргументации. Его спокойная и уверенная манера дала мне большую поддержку в это трудное время.",
          name: "Генрих Бауэр",
          designation: "Уголовное право",
          src: "/images/lawyers/testimonial3.jpg",
          caseType: "Уголовная защита",
          rating: 5,
          date: "Май 2023",
          location: "Гамбург",
        },
        {
          quote:
            "После моей автомобильной аварии г-жа Бауэр оказала неоценимую помощь в отстаивании моих требований. Она противостояла страховой компании и помогла мне получить справедливую компенсацию.",
          name: "София Шнайдер",
          designation: "Транспортное право",
          src: "/images/lawyers/testimonial4.jpg",
          caseType: "Урегулирование страхового случая",
          rating: 5,
          date: "Апрель 2023",
          location: "Франкфурт",
        },
        {
          quote:
            "Господин Хофман создал и проверил несколько сложных контрактов для нашей компании. Его точная работа и понимание требований нашего бизнеса уберегли нас от дорогостоящих ошибок.",
          name: "Томас Шмидт",
          designation: "Договорное право",
          src: "/images/lawyers/testimonial5.jpg",
          caseType: "Составление контрактов",
          rating: 4,
          date: "Июль 2023",
          location: "Кёльн",
        },
      ];
    } else if (dictionary.testimonials?.familyLaw === "Aile Hukuku") {
      // Turkish testimonials
      return [
        {
          quote:
            "Dr. Miller, karmaşık bir boşanma sürecinde bize empati ve en yüksek profesyonellikle rehberlik etti. Uzmanlığı sayesinde adil bir anlaşmaya varabildik.",
          name: "Mark ve Joanna Brendel",
          designation: "Aile Hukuku",
          src: "/images/lawyers/testimonial1.jpg",
          caseType: "Boşanma Davası",
          rating: 5,
          date: "Haziran 2023",
          location: "Münih",
        },
        {
          quote:
            "Haksız işten çıkarılmamdan sonra Dr. Schmidt yanımda durdu ve uygun bir tazminat paketini başarıyla uyguladı. Stratejik yaklaşımı ve müzakere becerileri etkileyiciydi.",
          name: "Anna Miller",
          designation: "İş Hukuku",
          src: "/images/lawyers/testimonial2.jpg",
          caseType: "İşten Çıkarılmaya Karşı Koruma",
          rating: 5,
          date: "Mart 2023",
          location: "Berlin",
        },
        {
          quote:
            "Bay Weber beni zor bir ceza davasında temsil etti ve titiz hazırlığı ve ikna edici argümanlarıyla beraat kazandı. Sakin ve kendinden emin tavrı, bu zor zamanda bana büyük destek verdi.",
          name: "Heinrich Bauer",
          designation: "Ceza Hukuku",
          src: "/images/lawyers/testimonial3.jpg",
          caseType: "Ceza Savunması",
          rating: 5,
          date: "Mayıs 2023",
          location: "Hamburg",
        },
        {
          quote:
            "Trafik kazamdan sonra Bayan Bauer, taleplerimizi uygulamada paha biçilmez bir yardım oldu. Sigorta şirketine karşı durdu ve adil bir tazminat almama yardımcı oldu.",
          name: "Sofia Schneider",
          designation: "Trafik Hukuku",
          src: "/images/lawyers/testimonial4.jpg",
          caseType: "Kaza Uzlaşması",
          rating: 5,
          date: "Nisan 2023",
          location: "Frankfurt",
        },
        {
          quote:
            "Bay Hoffman şirketimiz için birkaç karmaşık sözleşme oluşturdu ve inceledi. Titiz çalışması ve işimizin gereksinimlerini anlaması bizi maliyetli hatalardan korudu.",
          name: "Thomas Schmidt",
          designation: "Sözleşme Hukuku",
          src: "/images/lawyers/testimonial5.jpg",
          caseType: "Sözleşme Tasarımı",
          rating: 4,
          date: "Temmuz 2023",
          location: "Köln",
        },
      ];
    } else if (dictionary.testimonials?.familyLaw === "قانون الأسرة") {
      // Arabic testimonials
      return [
        {
          quote:
            "قاد د. ميلر عملية طلاق معقدة بتعاطف ومهنية عالية. بفضل خبرته، تمكنا من التوصل إلى اتفاق عادل.",
          name: "مارك وجوانا برينديل",
          designation: "قانون الأسرة",
          src: "/images/lawyers/testimonial1.jpg",
          caseType: "إجراءات الطلاق",
          rating: 5,
          date: "يونيو 2023",
          location: "ميونخ",
        },
        {
          quote:
            "بعد فصلي غير المبرر، وقفت د. شميدت بجانبي ونجحت في تنفيذ حزمة تعويضات مناسبة. كان نهجها الاستراتيجي ومهارات التفاوض مثيرة للإعجاب.",
          name: "آنا ميلر",
          designation: "قانون العمل",
          src: "/images/lawyers/testimonial2.jpg",
          caseType: "الحماية من الفصل",
          rating: 5,
          date: "مارس 2023",
          location: "برلين",
        },
        {
          quote:
            "مثلني السيد ويبر في قضية جنائية صعبة وحقق البراءة من خلال إعداده الدقيق وحججه المقنعة. أسلوبه الهادئ والواثق منحني دعمًا كبيرًا خلال هذا الوقت العصيب.",
          name: "هاينريش باور",
          designation: "القانون الجنائي",
          src: "/images/lawyers/testimonial3.jpg",
          caseType: "الدفاع الجنائي",
          rating: 5,
          date: "مايو 2023",
          location: "هامبورغ",
        },
        {
          quote:
            "بعد حادث المرور الخاص بي، كانت السيدة باور مساعدة لا تقدر بثمن في إنفاذ مطالباتي. وقفت ضد شركة التأمين وساعدتني في الحصول على تعويض عادل.",
          name: "صوفيا شنايدر",
          designation: "قانون المرور",
          src: "/images/lawyers/testimonial4.jpg",
          caseType: "تسوية الحوادث",
          rating: 5,
          date: "أبريل 2023",
          location: "فرانكفورت",
        },
        {
          quote:
            "أنشأ السيد هوفمان وراجع العديد من العقود المعقدة لشركتنا. عمله الدقيق وفهمه لمتطلبات أعمالنا أنقذنا من أخطاء مكلفة.",
          name: "توماس شميدت",
          designation: "قانون العقود",
          src: "/images/lawyers/testimonial5.jpg",
          caseType: "تصميم العقود",
          rating: 4,
          date: "يوليو 2023",
          location: "كولونيا",
        },
      ];
    } else {
      // Default to German testimonials
      return [
        {
          quote:
            "Dr. Müller hat uns durch einen komplexen Scheidungsprozess begleitet und dabei stets Einfühlungsvermögen und höchste Professionalität bewiesen. Dank seiner Expertise konnten wir eine faire Einigung erzielen.",
          name: "Markus & Johanna Brendel",
          designation: "Familienrecht",
          src: "/images/lawyers/testimonial1.jpg",
          caseType: "Scheidungsverfahren",
          rating: 5,
          date: "Juni 2023",
          location: "München",
        },
        {
          quote:
            "Nach meiner ungerechtfertigten Kündigung stand Frau Dr. Schmidt mir zur Seite und hat erfolgreich eine angemessene Abfindung durchgesetzt. Ihre strategische Vorgehensweise und ihr Verhandlungsgeschick waren beeindruckend.",
          name: "Anna Müller",
          designation: "Arbeitsrecht",
          src: "/images/lawyers/testimonial2.jpg",
          caseType: "Kündigungsschutzklage",
          rating: 5,
          date: "März 2023",
          location: "Berlin",
        },
        {
          quote:
            "Herr Weber hat mich in einem schwierigen Strafverfahren vertreten und durch seine akribische Vorbereitung und überzeugende Argumentation einen Freispruch erwirkt. Seine ruhige und zuversichtliche Art gab mir in dieser schweren Zeit viel Halt.",
          name: "Heinrich Bauer",
          designation: "Strafrecht",
          src: "/images/lawyers/testimonial3.jpg",
          caseType: "Strafverteidigung",
          rating: 5,
          date: "Mai 2023",
          location: "Hamburg",
        },
        {
          quote:
            "Nach meinem Verkehrsunfall war Frau Bauer eine unschätzbare Hilfe bei der Durchsetzung meiner Ansprüche. Sie hat sich gegen die Versicherung durchgesetzt und mir zu einer gerechten Entschädigung verholfen.",
          name: "Sofia Schneider",
          designation: "Verkehrsrecht",
          src: "/images/lawyers/testimonial4.jpg",
          caseType: "Unfallregulierung",
          rating: 5,
          date: "April 2023",
          location: "Frankfurt",
        },
        {
          quote:
            "Herr Hoffmann hat für unser Unternehmen mehrere komplexe Verträge erstellt und geprüft. Seine präzise Arbeitsweise und sein Verständnis für unsere Geschäftsanforderungen haben uns vor kostspieligen Fehlern bewahrt.",
          name: "Thomas Schmidt",
          designation: "Vertragsrecht",
          src: "/images/lawyers/testimonial5.jpg",
          caseType: "Vertragsgestaltung",
          rating: 4,
          date: "Juli 2023",
          location: "Köln",
        },
      ];
    }
  };

  // Get testimonials based on language
  const testimonials: Testimonial[] = getLocalizedTestimonials();

  // State for carousel
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);
  const { ref: sectionRef, inView } = useInView({
    triggerOnce: false,
    threshold: 0.1,
  });

  // Function to go to next slide
  const nextSlide = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prevIndex) =>
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
    setTimeout(() => setIsAnimating(false), 500);
  };

  // Function to go to previous slide
  const prevSlide = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
    setTimeout(() => setIsAnimating(false), 500);
  };

  // Auto-rotation effect
  useEffect(() => {
    if (inView) {
      autoPlayRef.current = setInterval(() => {
        nextSlide();
      }, 6000); // 6 seconds rotation
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [inView, currentIndex, isAnimating]);

  // Function to render star rating
  const renderStars = (rating: number) => {
    return Array(5)
      .fill(0)
      .map((_, i) => (
        <Star
          key={i}
          className={cn(
            "w-4 h-4",
            i < rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
          )}
        />
      ));
  };

  return (
    <Section
      className="py-12 sm:py-16 md:py-24 overflow-visible relative bg-gradient-to-b from-white to-slate-100"
      id="testimonials"
      aria-labelledby="testimonials-heading"
    >
      <div className="relative bg-white dark:bg-gray-900/80 backdrop-blur-sm p-4 sm:p-6 md:p-8 rounded-2xl shadow-lg max-w-7xl mx-auto">
        {/* Law firm logo and section title */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="relative w-16 h-16 mb-4">
              <div className="absolute inset-0 bg-blue-600 rounded-full flex items-center justify-center">
                <Scale className="w-8 h-8 text-white" />
              </div>
            </div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900 dark:text-white">
            {dictionary.testimonials?.title || "Was unsere Mandanten sagen"}
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            {dictionary.testimonials?.description ||
              "Erfahren Sie, was unsere Mandanten über ihre Erfahrungen mit unserer Kanzlei berichten."}
          </p>
        </div>

        {/* Testimonial Carousel */}
        <div className="relative max-w-4xl mx-auto mb-16">
          {/* Testimonial Card */}
          <div className="relative overflow-hidden">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.5 }}
                className="bg-slate-50 dark:bg-slate-800 rounded-xl p-8 shadow-md relative"
              >
                {/* Quote Icon */}
                <div className="absolute top-6 right-6 text-blue-100 dark:text-blue-900">
                  <Quote className="w-20 h-20 opacity-20" />
                </div>

                <div className="flex flex-col md:flex-row gap-8 items-center">
                  {/* Client Image */}
                  <div className="relative w-24 h-24 md:w-32 md:h-32 flex-shrink-0">
                    <div className="w-full h-full rounded-full overflow-hidden border-4 border-white shadow-md">
                      <Image
                        src={testimonials[currentIndex].src}
                        alt={`Porträt von ${testimonials[currentIndex].name}`}
                        width={128}
                        height={128}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    {/* Star Rating */}
                    <div className="flex mb-2">
                      {renderStars(testimonials[currentIndex].rating)}
                    </div>

                    {/* Quote */}
                    <p className="text-gray-700 dark:text-gray-300 mb-4 italic relative">
                      {testimonials[currentIndex].quote}
                    </p>

                    {/* Client Info */}
                    <div className="flex items-center justify-between flex-wrap">
                      <div>
                        <h3 className="font-bold text-gray-900 dark:text-white">
                          {testimonials[currentIndex].name}
                        </h3>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {testimonials[currentIndex].designation}
                        </div>
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-500 text-right">
                        <div>{testimonials[currentIndex].caseType}</div>
                        <div>
                          {testimonials[currentIndex].date} •{" "}
                          {testimonials[currentIndex].location}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevSlide}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 sm:-translate-x-8 bg-white dark:bg-gray-800 w-10 h-10 rounded-full shadow-md flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700"
            aria-label="Previous testimonial"
          >
            <ChevronLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
          <button
            onClick={nextSlide}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 sm:translate-x-8 bg-white dark:bg-gray-800 w-10 h-10 rounded-full shadow-md flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700"
            aria-label="Next testimonial"
          >
            <ChevronRight className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>

          {/* Indicators */}
          <div className="flex justify-center mt-6 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                  index === currentIndex
                    ? "bg-blue-600"
                    : "bg-gray-300 dark:bg-gray-700 hover:bg-blue-400"
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              ></button>
            ))}
          </div>
        </div>

        {/* Testimonial Submission Form */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 md:p-8 max-w-4xl mx-auto">
          <div className="flex items-center mb-6">
            <MessageSquare className="w-6 h-6 text-blue-600 dark:text-blue-400 mr-3" />
            <h3 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white">
              {dictionary.testimonials?.formTitle ||
                "Teilen Sie Ihre Erfahrung"}
            </h3>
          </div>

          <form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {dictionary.testimonials?.name || "Name"}
                </label>
                <input
                  type="text"
                  id="name"
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                  placeholder={dictionary.testimonials?.yourName || "Ihr Name"}
                />
              </div>
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {dictionary.testimonials?.email || "Email"}
                </label>
                <input
                  type="email"
                  id="email"
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                  placeholder={
                    dictionary.testimonials?.yourEmail || "Ihre Email"
                  }
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="caseType"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {dictionary.testimonials?.caseType || "Rechtsgebiet"}
              </label>
              <select
                id="caseType"
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
              >
                <option value="">
                  {dictionary.testimonials?.selectOption || "Bitte wählen Sie"}
                </option>
                <option value="Familienrecht">
                  {dictionary.testimonials?.familyLaw || "Familienrecht"}
                </option>
                <option value="Arbeitsrecht">
                  {dictionary.testimonials?.employmentLaw || "Arbeitsrecht"}
                </option>
                <option value="Strafrecht">
                  {dictionary.testimonials?.criminalLaw || "Strafrecht"}
                </option>
                <option value="Verkehrsrecht">
                  {dictionary.testimonials?.trafficLaw || "Verkehrsrecht"}
                </option>
                <option value="Vertragsrecht">
                  {dictionary.testimonials?.contractLaw || "Vertragsrecht"}
                </option>
              </select>
            </div>

            <div>
              <label
                htmlFor="rating"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {dictionary.testimonials?.rating || "Bewertung"}
              </label>
              <div className="flex space-x-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    type="button"
                    className="text-gray-300 hover:text-yellow-400 focus:outline-none focus:text-yellow-400"
                    aria-label={`${star} ${
                      dictionary.testimonials?.stars || "Sterne"
                    }`}
                  >
                    <Star className="w-6 h-6" />
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label
                htmlFor="message"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                {dictionary.testimonials?.yourExperience || "Ihre Erfahrung"}
              </label>
              <textarea
                id="message"
                rows={4}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                placeholder={
                  dictionary.testimonials?.experienceDescription ||
                  "Beschreiben Sie Ihre Erfahrung mit unserer Kanzlei..."
                }
              ></textarea>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
              >
                <span>
                  {dictionary.testimonials?.submitReview || "Bewertung senden"}
                </span>
                <Send className="ml-2 w-4 h-4" />
              </button>
            </div>
          </form>
        </div>

        {/* Decorative elements */}
        <div
          className="absolute top-0 left-0 w-96 h-96 bg-blue-500/5 dark:bg-blue-500/10 rounded-full -translate-x-1/2 -translate-y-1/2 blur-3xl"
          aria-hidden="true"
        />
        <div
          className="absolute bottom-0 right-0 w-96 h-96 bg-primary/5 dark:bg-primary/10 rounded-full translate-x-1/2 translate-y-1/2 blur-3xl"
          aria-hidden="true"
        />
        <div
          className="absolute top-1/3 right-1/4 w-64 h-64 bg-purple-500/5 dark:bg-purple-500/10 rounded-full blur-3xl"
          aria-hidden="true"
        />
      </div>
    </Section>
  );
};
