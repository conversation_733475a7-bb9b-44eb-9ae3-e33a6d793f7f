'use client'

import React, { useState } from 'react'
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Section } from "@/components/ui/Section";
import { Button } from "@/components/ui/Button";
import { useI18n } from "@/providers/I18nProvider";
import { type Dictionary } from "@/lib/dictionary";

// Contact details - replace with your own
const contactInfo = {
  email: "<EMAIL>",
  phone: "+****************",
  location: "New York, NY",
  calendlyLink: "https://calendly.com/your-business", // Add your actual Calendly link here
};

interface ContactSectionProps {
  dictionary: Dictionary["contact"] & {
    location?: string; // Add location type
    messageReceivedThankYou?: string; // Add thank you message type
  };
}

export const ContactSection = ({ dictionary }: ContactSectionProps) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const [formStatus, setFormStatus] = useState<
    "idle" | "submitting" | "success" | "error"
  >("idle");
  const [hoveredContact, setHoveredContact] = useState<string | null>(null);
  const { t, dir } = useI18n();
  const isRtl = dir === "rtl";

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Create mailto URL with form data
    const subject = encodeURIComponent(formData.subject);
    const body = encodeURIComponent(`
Name: ${formData.name}
Email: ${formData.email}

Message:
${formData.message}
    `);

    // Open email client with pre-filled content
    window.location.href = `mailto:${contactInfo.email}?subject=${subject}&body=${body}`;

    // Reset form
    setFormData({
      name: "",
      email: "",
      subject: "",
      message: "",
    });

    // Show success message
    setFormStatus("success");

    // Reset status after 3 seconds
    setTimeout(() => {
      setFormStatus("idle");
    }, 3000);
  };

  const contactMethods = [
    {
      id: "email",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
      ),
      title: dictionary?.email || "Email",
      value: contactInfo.email,
      action: `mailto:${contactInfo.email}`,
      color: "#3B82F6", // blue-500
    },
    {
      id: "phone",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
          />
        </svg>
      ),
      title: dictionary?.phone || "Phone",
      value: contactInfo.phone,
      action: `tel:${contactInfo.phone}`,
      color: "#10B981", // emerald-500
    },
    {
      id: "calendar",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      ),
      title: "Schedule a Call",
      value: "Book a free 15-min consultation",
      action: contactInfo.calendlyLink,
      color: "#8B5CF6", // violet-500
    },
    {
      id: "location",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
          />
        </svg>
      ),
      title: dictionary?.location || "Location",
      value: contactInfo.location,
      action: "#", // No direct action for location
      color: "#F59E0B", // amber-500
    },
  ];

  return (
    <Section
      id="contact"
      className="w-full py-16 sm:py-24 bg-gray-50 dark:bg-gray-900 relative overflow-hidden"
      headingLevel="h2"
      aria-labelledby="contact-heading"
    >
      {/* Contact Banner */}
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl shadow-xl mb-16 overflow-hidden">
        <div className="flex flex-col md:flex-row items-center justify-between p-8 md:p-12">
          <div className="text-center md:text-left mb-6 md:mb-0">
            <h2
              id="contact-heading"
              className="text-white text-2xl md:text-3xl font-bold mb-3"
            >
              {dictionary?.title || "Get in touch with our team"}
            </h2>
            <p className="text-blue-100 text-lg md:max-w-md">
              {dictionary?.description ||
                "Have questions or ready to start your project? We're here to help turn your vision into reality."}
            </p>
          </div>
          <a
            href="#contact-form"
            className="inline-block px-6 py-3 bg-white text-blue-600 font-medium rounded-lg shadow-md hover:bg-blue-50 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-500"
            aria-label="Jump to contact form"
          >
            {dictionary?.send || "Contact Us Now"}
          </a>
        </div>
      </div>

      {/* Background elements - Adjusting colors slightly */}
      <div
        className="absolute top-0 right-0 w-[500px] h-[500px] bg-blue-500/5 dark:bg-blue-900/10 rounded-full translate-x-1/2 -translate-y-1/2 blur-3xl pointer-events-none -z-10"
        aria-hidden="true"
      />
      <div
        className="absolute bottom-0 left-0 w-[500px] h-[500px] bg-indigo-500/5 dark:bg-indigo-900/10 rounded-full -translate-x-1/2 translate-y-1/2 blur-3xl pointer-events-none -z-10"
        aria-hidden="true"
      />
      <div
        className="absolute inset-0 bg-grid-pattern opacity-3 pointer-events-none -z-10"
        aria-hidden="true"
      />

      <div className="grid md:grid-cols-2 gap-8 sm:gap-12 px-4 sm:px-6 md:px-0">
        {/* Contact Methods */}
        <motion.div
          className="relative"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h3
            className="text-2xl font-bold text-gray-900 dark:text-white mb-6"
            id="contact-info-heading"
          >
            {dictionary?.contactInfo || "Get In Touch"}
          </h3>

          <div className="space-y-6" aria-labelledby="contact-info-heading">
            {contactMethods.map((method) => (
              <a
                key={method.id}
                href={method.action}
                target={
                  method.id === "calendar" || method.id === "website"
                    ? "_blank"
                    : method.id === "location" // Don't open new tab for location
                    ? "_self"
                    : undefined
                }
                rel={
                  method.id === "calendar" || method.id === "website"
                    ? "noopener noreferrer"
                    : undefined
                }
                onMouseEnter={() => setHoveredContact(method.id)}
                onMouseLeave={() => setHoveredContact(null)}
                className="group block focus:outline-none focus:ring-2 focus:ring-blue-500 focus:rounded-lg"
                aria-label={`${method.title}: ${method.value}`}
              >
                <div className="flex items-start gap-4 p-4 rounded-xl transition-all duration-300 hover:bg-gray-100 dark:hover:bg-gray-800/50">
                  <div
                    className="p-3 rounded-lg transition-all duration-300"
                    style={{
                      backgroundColor:
                        hoveredContact === method.id
                          ? method.color
                          : `${method.color}20`,
                      color:
                        hoveredContact === method.id ? "white" : method.color,
                    }}
                  >
                    {React.cloneElement(method.icon, { "aria-hidden": true })}
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                      {method.title}
                    </h4>
                    <p
                      className="text-gray-700 dark:text-gray-300 transition-all"
                      style={{
                        color: hoveredContact === method.id ? method.color : "",
                      }}
                    >
                      {method.value}
                    </p>
                  </div>
                </div>
              </a>
            ))}
          </div>
        </motion.div>

        {/* Contact Form */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <form
            onSubmit={handleSubmit}
            className="bg-white dark:bg-gray-800 p-5 sm:p-6 md:p-8 rounded-2xl shadow-lg"
            id="contact-form"
            aria-labelledby="contact-form-heading"
            noValidate
          >
            <h3
              className="text-2xl font-bold text-gray-900 dark:text-white mb-6"
              id="contact-form-heading"
            >
              {dictionary?.writeUs || "Send a Message"}
            </h3>

            <div className="space-y-4">
              <div>
                <label
                  htmlFor="contact-form-name"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {dictionary?.name || "Name"}{" "}
                  <span className="text-red-500" aria-hidden="true">
                    *
                  </span>
                </label>
                <input
                  type="text"
                  id="contact-form-name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  aria-required="true"
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700/50 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                  placeholder={dictionary?.yourName || "Your Name"}
                  disabled={formStatus === "submitting"}
                  aria-describedby={
                    formStatus === "error" ? "name-error" : undefined
                  }
                />
              </div>

              <div>
                <label
                  htmlFor="contact-form-email"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {dictionary?.email || "Email"}{" "}
                  <span className="text-red-500" aria-hidden="true">
                    *
                  </span>
                </label>
                <input
                  type="email"
                  id="contact-form-email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  aria-required="true"
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700/50 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                  placeholder={dictionary?.yourEmail || "Your Email"}
                  disabled={formStatus === "submitting"}
                  aria-describedby={
                    formStatus === "error" ? "email-error" : undefined
                  }
                  autoComplete="email"
                />
              </div>

              <div>
                <label
                  htmlFor="contact-form-subject"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {dictionary?.subject || "Subject"}{" "}
                  <span className="text-red-500" aria-hidden="true">
                    *
                  </span>
                </label>
                <input
                  type="text"
                  id="contact-form-subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  required
                  aria-required="true"
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700/50 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                  placeholder={dictionary?.howCanIHelp || "How can I help?"}
                  disabled={formStatus === "submitting"}
                  aria-describedby={
                    formStatus === "error" ? "subject-error" : undefined
                  }
                />
              </div>

              <div>
                <label
                  htmlFor="contact-form-message"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  {dictionary?.message || "Message"}{" "}
                  <span className="text-red-500" aria-hidden="true">
                    *
                  </span>
                </label>
                <textarea
                  id="contact-form-message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  aria-required="true"
                  rows={5}
                  className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700/50 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"
                  placeholder={
                    dictionary?.yourMessageHere || "Your message here"
                  }
                  disabled={formStatus === "submitting"}
                  aria-describedby={
                    formStatus === "error" ? "message-error" : undefined
                  }
                />
              </div>

              <div>
                <Button
                  type="submit"
                  className="w-full bg-[#165dfb] hover:bg-[#165dfb]/90 text-white py-3 rounded-lg transition-all flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2"
                  disabled={formStatus === "submitting"}
                  aria-live="polite"
                >
                  {formStatus === "submitting" ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      {dictionary?.sendingMessage || "Sending..."}
                    </>
                  ) : formStatus === "success" ? (
                    <>
                      <svg
                        className="h-5 w-5 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M5 13l4 4L19 7"
                        ></path>
                      </svg>
                      {dictionary?.messageSent || "Message Sent!"}
                    </>
                  ) : formStatus === "error" ? (
                    <>
                      <svg
                        className="h-5 w-5 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M6 18L18 6M6 6l12 12"
                        ></path>
                      </svg>
                      {dictionary?.messageFailed || "Error, try again"}
                    </>
                  ) : (
                    dictionary?.send || "Send Message"
                  )}
                </Button>
              </div>

              <div
                className="text-center pt-2 text-sm text-gray-500 dark:text-gray-400"
                aria-live="polite"
              >
                {formStatus === "success" ? (
                  <p
                    className="text-green-600 dark:text-green-400 font-medium"
                    id="form-success-message"
                  >
                    {dictionary?.messageReceivedThankYou ||
                      "Thank you for your message! We'll get back to you soon."}
                  </p>
                ) : (
                  <p>
                    {dictionary?.orSchedule ||
                      "Or schedule a meeting directly using the calendar link"}
                  </p>
                )}
              </div>
            </div>
          </form>
        </motion.div>
      </div>
    </Section>
  );
};