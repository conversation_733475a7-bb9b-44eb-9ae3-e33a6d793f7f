'use client'

import React, { useState } from 'react'
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Section } from "@/components/ui/Section";
import { Button } from "@/components/ui/Button";
import { useI18n } from "@/providers/I18nProvider";
import { type Dictionary } from "@/lib/dictionary";

// Contact details - replace with your own
const contactInfo = {
  email: "<EMAIL>",
  phone: "+****************",
  location: "New York, NY",
  calendlyLink: "https://calendly.com/your-business", // Add your actual Calendly link here
};

interface ContactSectionProps {
  dictionary: Dictionary["contact"] & {
    location?: string; // Add location type
    messageReceivedThankYou?: string; // Add thank you message type
  };
}

export const ContactSection = ({ dictionary }: ContactSectionProps) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const [formStatus, setFormStatus] = useState<
    "idle" | "submitting" | "success" | "error"
  >("idle");
  const [hoveredContact, setHoveredContact] = useState<string | null>(null);
  const { t, dir } = useI18n();
  const isRtl = dir === "rtl";

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Create mailto URL with form data
    const subject = encodeURIComponent(formData.subject);
    const body = encodeURIComponent(`
Name: ${formData.name}
Email: ${formData.email}

Message:
${formData.message}
    `);

    // Open email client with pre-filled content
    window.location.href = `mailto:${contactInfo.email}?subject=${subject}&body=${body}`;

    // Reset form
    setFormData({
      name: "",
      email: "",
      subject: "",
      message: "",
    });

    // Show success message
    setFormStatus("success");

    // Reset status after 3 seconds
    setTimeout(() => {
      setFormStatus("idle");
    }, 3000);
  };

  const contactMethods = [
    {
      id: "email",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
      ),
      title: dictionary?.email || "Email",
      value: contactInfo.email,
      action: `mailto:${contactInfo.email}`,
      color: "#3B82F6", // blue-500
    },
    {
      id: "phone",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
          />
        </svg>
      ),
      title: dictionary?.phone || "Phone",
      value: contactInfo.phone,
      action: `tel:${contactInfo.phone}`,
      color: "#10B981", // emerald-500
    },
    {
      id: "calendar",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      ),
      title: "Schedule a Call",
      value: "Book a free 15-min consultation",
      action: contactInfo.calendlyLink,
      color: "#8B5CF6", // violet-500
    },
    {
      id: "location",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-8 w-8"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
          />
        </svg>
      ),
      title: dictionary?.location || "Location",
      value: contactInfo.location,
      action: "#", // No direct action for location
      color: "#F59E0B", // amber-500
    },
  ];

  return (
    <Section
      id="contact"
      className="py-16 sm:py-20 md:py-28 bg-white dark:bg-gray-900 relative overflow-hidden"
      headingLevel="h2"
      aria-labelledby="contact-heading"
    >
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20 dark:from-blue-900/10 dark:via-transparent dark:to-indigo-900/5"></div>
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-200/50 dark:via-blue-700/30 to-transparent"></div>

      <div className="container mx-auto px-6 max-w-7xl relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-6"
          >
            <span className="inline-block px-6 py-3 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium">
              Kontakt
            </span>
          </motion.div>
          <motion.h2
            id="contact-heading"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-5xl md:text-6xl font-bold mb-6 text-gray-900 dark:text-white leading-tight"
          >
            {dictionary?.title || "Lassen Sie uns sprechen"}
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed"
          >
            {dictionary?.description ||
              "Haben Sie Fragen oder benötigen rechtliche Beratung? Kontaktieren Sie uns für eine kostenlose Erstberatung."}
          </motion.p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16">
          {/* Contact Methods */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h3
              className="text-3xl font-bold text-gray-900 dark:text-white mb-8"
              id="contact-info-heading"
            >
              {dictionary?.contactInfo || "Kontaktinformationen"}
            </h3>

            <div className="space-y-6" aria-labelledby="contact-info-heading">
              {contactMethods.map((method, index) => (
                <motion.a
                  key={method.id}
                  href={method.action}
                  target={
                    method.id === "calendar" || method.id === "website"
                      ? "_blank"
                      : method.id === "location"
                      ? "_self"
                      : undefined
                  }
                  rel={
                    method.id === "calendar" || method.id === "website"
                      ? "noopener noreferrer"
                      : undefined
                  }
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                  whileHover={{ scale: 1.02, y: -2 }}
                  onMouseEnter={() => setHoveredContact(method.id)}
                  onMouseLeave={() => setHoveredContact(null)}
                  className="group block focus:outline-none focus:ring-2 focus:ring-blue-500 focus:rounded-2xl"
                  aria-label={`${method.title}: ${method.value}`}
                >
                  <div className="flex items-start gap-5 p-6 rounded-2xl bg-white dark:bg-gray-800/50 border border-gray-200/50 dark:border-gray-700/50 shadow-sm hover:shadow-lg transition-all duration-300 backdrop-blur-sm">
                    <div
                      className="p-4 rounded-xl transition-all duration-300 shadow-sm"
                      style={{
                        backgroundColor:
                          hoveredContact === method.id
                            ? method.color
                            : `${method.color}15`,
                        color:
                          hoveredContact === method.id ? "white" : method.color,
                      }}
                    >
                      {React.cloneElement(method.icon, { "aria-hidden": true })}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                        {method.title}
                      </h4>
                      <p
                        className="text-gray-600 dark:text-gray-300 transition-all duration-300 font-medium"
                        style={{
                          color: hoveredContact === method.id ? method.color : "",
                        }}
                      >
                        {method.value}
                      </p>
                    </div>
                  </div>
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <form
              onSubmit={handleSubmit}
              className="bg-white dark:bg-gray-800/50 backdrop-blur-sm p-8 md:p-10 rounded-3xl shadow-xl border border-gray-200/50 dark:border-gray-700/50"
              id="contact-form"
              aria-labelledby="contact-form-heading"
              noValidate
            >
              <h3
                className="text-3xl font-bold text-gray-900 dark:text-white mb-8"
                id="contact-form-heading"
              >
                {dictionary?.writeUs || "Nachricht senden"}
              </h3>

              <div className="space-y-6">
                <div>
                  <label
                    htmlFor="contact-form-name"
                    className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                  >
                    {dictionary?.name || "Name"}{" "}
                    <span className="text-red-500" aria-hidden="true">
                      *
                    </span>
                  </label>
                  <input
                    type="text"
                    id="contact-form-name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    aria-required="true"
                    className="w-full px-5 py-4 rounded-2xl border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/50 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder={dictionary?.yourName || "Ihr vollständiger Name"}
                    disabled={formStatus === "submitting"}
                    aria-describedby={
                      formStatus === "error" ? "name-error" : undefined
                    }
                  />
                </div>

                <div>
                  <label
                    htmlFor="contact-form-email"
                    className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                  >
                    {dictionary?.email || "E-Mail"}{" "}
                    <span className="text-red-500" aria-hidden="true">
                      *
                    </span>
                  </label>
                  <input
                    type="email"
                    id="contact-form-email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    aria-required="true"
                    className="w-full px-5 py-4 rounded-2xl border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/50 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder={dictionary?.yourEmail || "<EMAIL>"}
                    disabled={formStatus === "submitting"}
                    aria-describedby={
                      formStatus === "error" ? "email-error" : undefined
                    }
                    autoComplete="email"
                  />
                </div>

                <div>
                  <label
                    htmlFor="contact-form-subject"
                    className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                  >
                    {dictionary?.subject || "Betreff"}{" "}
                    <span className="text-red-500" aria-hidden="true">
                      *
                    </span>
                  </label>
                  <input
                    type="text"
                    id="contact-form-subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    aria-required="true"
                    className="w-full px-5 py-4 rounded-2xl border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/50 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder={dictionary?.howCanIHelp || "Womit können wir Ihnen helfen?"}
                    disabled={formStatus === "submitting"}
                    aria-describedby={
                      formStatus === "error" ? "subject-error" : undefined
                    }
                  />
                </div>

                <div>
                  <label
                    htmlFor="contact-form-message"
                    className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                  >
                    {dictionary?.message || "Nachricht"}{" "}
                    <span className="text-red-500" aria-hidden="true">
                      *
                    </span>
                  </label>
                  <textarea
                    id="contact-form-message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    aria-required="true"
                    rows={6}
                    className="w-full px-5 py-4 rounded-2xl border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/50 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 placeholder-gray-500 dark:placeholder-gray-400 resize-none"
                    placeholder={
                      dictionary?.yourMessageHere || "Beschreiben Sie Ihr Anliegen ausführlich..."
                    }
                    disabled={formStatus === "submitting"}
                    aria-describedby={
                      formStatus === "error" ? "message-error" : undefined
                    }
                  />
                </div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    type="submit"
                    className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white py-4 px-8 rounded-2xl font-semibold text-lg transition-all duration-300 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 shadow-lg hover:shadow-xl"
                    disabled={formStatus === "submitting"}
                    aria-live="polite"
                  >
                  {formStatus === "submitting" ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      {dictionary?.sendingMessage || "Wird gesendet..."}
                    </>
                  ) : formStatus === "success" ? (
                    <>
                      <svg
                        className="h-5 w-5 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M5 13l4 4L19 7"
                        ></path>
                      </svg>
                      {dictionary?.messageSent || "Nachricht gesendet!"}
                    </>
                  ) : formStatus === "error" ? (
                    <>
                      <svg
                        className="h-5 w-5 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M6 18L18 6M6 6l12 12"
                        ></path>
                      </svg>
                      {dictionary?.messageFailed || "Fehler, erneut versuchen"}
                    </>
                  ) : (
                    <>
                      <svg
                        className="h-5 w-5 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                        ></path>
                      </svg>
                      {dictionary?.send || "Nachricht senden"}
                    </>
                  )}
                </Button>
              </div>

              <div
                className="text-center pt-2 text-sm text-gray-500 dark:text-gray-400"
                aria-live="polite"
              >
                {formStatus === "success" ? (
                  <p
                    className="text-green-600 dark:text-green-400 font-medium"
                    id="form-success-message"
                  >
                    {dictionary?.messageReceivedThankYou ||
                      "Thank you for your message! We'll get back to you soon."}
                  </p>
                ) : (
                  <p>
                    {dictionary?.orSchedule ||
                      "Or schedule a meeting directly using the calendar link"}
                  </p>
                )}
              </div>
            </div>
          </form>
        </motion.div>
      </div>
    </Section>
  );
};