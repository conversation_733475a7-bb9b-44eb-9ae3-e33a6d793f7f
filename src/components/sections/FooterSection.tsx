'use client'

import { useState } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { useI18n } from '@/providers/I18nProvider'
import { type Dictionary } from '@/lib/dictionary'

// Social media links
const socialLinks = [
  {
    name: "Email",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
        />
      </svg>
    ),
    url: "mailto:<EMAIL>",
  },
  {
    name: "Phone",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
        />
      </svg>
    ),
    url: "tel:+4930123456789",
  },
  {
    name: "Facebook",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5"
        fill="currentColor"
        viewBox="0 0 24 24"
      >
        <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z" />
      </svg>
    ),
    url: "https://www.facebook.com/",
  },
  {
    name: "LinkedIn",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5"
        fill="currentColor"
        viewBox="0 0 24 24"
      >
        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
      </svg>
    ),
    url: "https://www.linkedin.com/",
  },
];

// Navigation links
const navigationLinks = [
  { name: "nav.home", href: "#hero" },
  { name: "nav.services", href: "#services" },
  { name: "nav.expertise", href: "#expertise" },
  { name: "nav.about", href: "#about" },
  { name: "nav.team", href: "#team" },
  { name: "nav.testimonials", href: "#testimonials" },
  { name: "nav.contact", href: "#contact" },
];

// Contact details - replace with your own information
const contactInfo = {
  email: "<EMAIL>",
  phone: "+49 30 ***********",
  location: "Friedrichstraße 123, 10117 Berlin, Deutschland",
  calendlyLink: "https://calendly.com/anwaltskanzlei",
  whatsapp: "+49 30 ***********",
  linkedin: "https://www.linkedin.com/company/anwaltskanzlei",
  facebook: "https://www.facebook.com/anwaltskanzlei",
  instagram: "https://www.instagram.com/anwaltskanzlei",
  twitter: "https://twitter.com/anwaltskanzlei",
  booking: "https://calendly.com/anwaltskanzlei/erstberatung",
  directions: "https://maps.google.com/?q=Friedrichstraße+123+10117+Berlin+Deutschland",
};

// Law firm certifications and memberships
const certifications = [
  {
    name: "Deutscher Anwaltverein",
    logo: "/images/lawyers/dav-logo.png",
  },
  {
    name: "Bundesrechtsanwaltskammer",
    logo: "/images/lawyers/brak-logo.png",
  },
  {
    name: "Arbeitsgemeinschaft Familienrecht",
    logo: "/images/lawyers/arge-logo.png",
  },
];

interface FooterSectionProps {
  dictionary: Dictionary["footer"];
}

export const FooterSection = ({ dictionary }: FooterSectionProps) => {
  const [hoveredSocial, setHoveredSocial] = useState<string | null>(null);
  const { t, dir } = useI18n();
  const isRtl = dir === "rtl";

  return (
    <footer
      className={`w-full bg-gray-900 dark:bg-gray-950 text-white ${
        isRtl ? "rtl-section" : ""
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-8 sm:py-12">
        {/* Law Firm Certifications */}
        <div className="flex flex-wrap justify-center gap-8 mb-10 pb-10 border-b border-white/10">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-4 text-white">
              {t("footer.certifications") ||
                "Mitgliedschaften & Zertifizierungen"}
            </h3>
            <div className="flex flex-wrap justify-center gap-6">
              {certifications.map((cert) => (
                <div key={cert.name} className="flex flex-col items-center">
                  <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mb-2">
                    <div className="text-2xl font-bold text-gray-800">
                      {cert.name.charAt(0)}
                    </div>
                  </div>
                  <span className="text-sm text-gray-400">{cert.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
          {/* Company info */}
          <div className="space-y-4">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              {"Anwaltskanzlei Müller & Partner"}
            </h2>
            <p className="text-gray-400">{dictionary.description}</p>
            {/* Social links */}
            <div className="flex gap-4">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.name}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  onMouseEnter={() => setHoveredSocial(social.name)}
                  onMouseLeave={() => setHoveredSocial(null)}
                  whileHover={{ scale: 1.1 }}
                  className="p-2 rounded-lg transition-all duration-300 bg-white/5 hover:bg-white/10"
                  aria-label={social.name}
                >
                  <div className="text-white/70 hover:text-white transition-colors">
                    {social.icon}
                  </div>
                </motion.a>
              ))}
            </div>
          </div>

          {/* Quick links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">
              {t("footer.quickLinks") || "Quick Links"}
            </h3>
            <div className="flex flex-col space-y-2">
              {navigationLinks.map((link) => {
                // Check if translation exists, otherwise use fallback
                const translationKey = link.name;
                return (
                  <a
                    key={link.href}
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors duration-300"
                  >
                    {t(translationKey) || translationKey.split(".")[1]}
                  </a>
                );
              })}
            </div>
          </div>

          {/* Contact info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{t("Contact")}</h3>
            <ul className="space-y-3">
              <li>
                <a
                  href={`mailto:${contactInfo.email}`}
                  className="text-gray-400 hover:text-white transition-colors duration-300 flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  {contactInfo.email}
                </a>
              </li>
              <li>
                <a
                  href={`tel:${contactInfo.phone}`}
                  className="text-gray-400 hover:text-white transition-colors duration-300 flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                  {contactInfo.phone}
                </a>
              </li>
              <li>
                <a
                  href={contactInfo.directions}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors duration-300 flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  {contactInfo.location}
                </a>
              </li>
              <li className="pt-3 mt-3 border-t border-white/10">
                <h4 className="text-sm font-medium text-white mb-2">
                  {t("footer.openingHours") || "Opening Hours:"}
                </h4>
                <p className="text-gray-400 text-sm">
                  {t("footer.weekdayHours") || "Monday - Friday: 9:00 - 18:00"}
                </p>
                <p className="text-gray-400 text-sm">
                  {t("footer.appointmentsNote") ||
                    "Appointments outside opening hours by arrangement"}
                </p>
                <p className="text-gray-400 text-sm">
                  {t("footer.weekendHours") || "Weekend: Closed"}
                </p>
              </li>
            </ul>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="mt-10 pt-10 border-t border-white/10">
          <div className="max-w-xl mx-auto text-center">
            <h3 className="text-xl font-semibold mb-3 text-white">
              {t("footer.stayInformed") ||
                "Stay informed about legal developments"}
            </h3>
            <p className="text-gray-400 mb-6">
              {t("footer.legalUpdates") ||
                "Subscribe to our newsletter for legal updates, case law, and expert tips."}
            </p>

            <form className="flex flex-col sm:flex-row gap-3">
              <input
                type="email"
                placeholder={
                  t("footer.emailPlaceholder") || "Your email address"
                }
                className="flex-grow px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-white"
                required
              />
              <button
                type="submit"
                className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
              >
                {t("footer.subscribe") || "Subscribe"}
              </button>
            </form>
            <p className="text-gray-500 text-xs mt-3">
              {t("footer.privacyNote") ||
                "We respect your privacy. Unsubscribe at any time."}
            </p>
          </div>
        </div>

        {/* Bottom bar */}
        <div className="mt-6 sm:mt-8 pt-6 sm:pt-8 border-t border-white/10">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-gray-400 text-sm text-center md:text-left">
              {dictionary.copyright}
            </p>

            {/* Built with note */}
            <div className="flex flex-wrap items-center justify-center md:justify-start gap-2 text-gray-400 text-sm">
              <span>{t("footer.builtWith")}</span>
              <span className="text-blue-400">React</span>
              <span>{t("footer.and")}</span>
              <span className="text-purple-400">Next.js</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};