"use client";

import * as React from "react";
import { Section } from "@/components/ui/Section";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { type Dictionary } from "@/lib/dictionary";
import {
  Users,
  Award,
  Target,
  Heart,
  Check,
  Briefcase,
  Shield,
} from "lucide-react";
import { AnimatedCounter } from "@/components/ui/animated-counter";

interface AboutSectionProps {
  dictionary: Dictionary;
  clientsDictionary?: Dictionary["clients"];
}

// Law firm stats
const stats = [
  {
    icon: <Award className="h-8 w-8" />,
    value: 98,
    suffix: "%",
    key: "successRate" as keyof Dictionary["about"],
    fallback: "Erfolgsquote",
    color: "from-green-500 to-green-600",
  },
  {
    icon: <Briefcase className="h-8 w-8" />,
    value: 15,
    suffix: "+",
    key: "yearsExperience" as keyof Dictionary["about"],
    fallback: "Jahre Erfahrung",
    color: "from-blue-500 to-blue-600",
  },
  {
    icon: <Check className="h-8 w-8" />,
    value: 2000,
    suffix: "+",
    key: "casesWon" as keyof Dictionary["about"],
    fallback: "Erfolgreiche Fälle",
    color: "from-purple-500 to-purple-600",
  },
  {
    icon: <Users className="h-8 w-8" />,
    value: 4,
    suffix: "",
    key: "languages" as keyof Dictionary["about"],
    fallback: "Sprachen",
    color: "from-pink-500 to-pink-600",
  },
];

// Law firm values
const getValues = (dictionary: Dictionary) => [
  {
    icon: <Target className="h-6 w-6 text-blue-600" />,
    title:
      dictionary.about?.values?.clientOrientation?.title ||
      "Mandantenorientierung",
    description:
      dictionary.about?.values?.clientOrientation?.description ||
      "Jeder Fall ist einzigartig – wir bieten individuelle, persönliche Beratung und setzen uns engagiert für Ihre Rechte ein.",
  },
  {
    icon: <Award className="h-6 w-6 text-blue-600" />,
    title: dictionary.about?.values?.expertise?.title || "Fachkompetenz",
    description:
      dictionary.about?.values?.expertise?.description ||
      "Langjährige Erfahrung und kontinuierliche Weiterbildung garantieren höchste juristische Qualität.",
  },
  {
    icon: <Check className="h-6 w-6 text-blue-600" />,
    title: dictionary.about?.values?.integrity?.title || "Integrität",
    description:
      dictionary.about?.values?.integrity?.description ||
      "Transparenz, Ehrlichkeit und Diskretion sind die Grundpfeiler unserer Arbeit.",
  },
  {
    icon: <Heart className="h-6 w-6 text-blue-600" />,
    title: dictionary.about?.values?.commitment?.title || "Engagement",
    description:
      dictionary.about?.values?.commitment?.description ||
      "Wir kämpfen leidenschaftlich und zielstrebig für die Interessen unserer Mandanten.",
  },
];

export const AboutSection = ({ dictionary, clientsDictionary }: AboutSectionProps) => {
  // Intersection observer refs for animations
  const { ref: companyRef, inView: companyInView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Get translatable content
  const values = getValues(dictionary);

  return (
    <Section
      className="py-16 sm:py-20 md:py-28 bg-white dark:bg-gray-900 overflow-hidden relative"
      id="about"
    >
      {/* Subtle background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20 dark:from-blue-900/10 dark:via-transparent dark:to-indigo-900/5"></div>
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-200/50 dark:via-blue-700/30 to-transparent"></div>

      <div className="container mx-auto px-6 max-w-7xl relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-6"
          >
            <span className="inline-block px-6 py-3 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm font-medium">
              Über uns
            </span>
          </motion.div>
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-5xl md:text-6xl font-bold mb-6 text-gray-900 dark:text-white leading-tight"
          >
            {dictionary.about?.title || "Ihre Anwaltskanzlei"}
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed"
          >
            {dictionary.about?.description ||
              "Seit über 15 Jahren stehen wir für kompetente Rechtsberatung und erfolgreiche Mandatsführung. Vertrauen Sie auf unsere Erfahrung und Expertise."}
          </motion.p>
        </div>

        {/* Stats Section */}
        <motion.div 
          ref={companyRef}
          initial={{ opacity: 0, y: 40 }}
          animate={companyInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="mb-20"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.key}
                initial={{ opacity: 0, y: 20 }}
                animate={companyInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                className="text-center"
              >
                <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br ${stat.color} flex items-center justify-center text-white`}>
                  {stat.icon}
                </div>
                <div className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2">
                  <AnimatedCounter 
                    value={stat.value} 
                    suffix={stat.suffix}
                    duration={2000}
                    delay={500 + index * 100}
                  />
                </div>
                <p className="text-gray-600 dark:text-gray-300 font-medium">
                  {dictionary.about?.[stat.key] || stat.fallback}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Values Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <motion.h3 
              initial={{ opacity: 0, y: 20 }}
              animate={companyInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4"
            >
              Unsere Werte
            </motion.h3>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={companyInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto"
            >
              Diese Grundsätze leiten unser tägliches Handeln und prägen unsere Arbeitsweise.
            </motion.p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 30 }}
                animate={companyInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{ duration: 0.6, delay: 0.7 + index * 0.1 }}
                className="bg-white dark:bg-gray-800/50 p-6 rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover:shadow-lg transition-all duration-300 group"
              >
                <div className="w-12 h-12 bg-blue-50 dark:bg-blue-900/30 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  {value.icon}
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                  {value.title}
                </h4>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </Section>
  );
};
