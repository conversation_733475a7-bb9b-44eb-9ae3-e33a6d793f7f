"use client";

import * as React from "react";
import { Section } from "@/components/ui/Section";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { type Dictionary } from "@/lib/dictionary";
import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  Users,
  Building2,
  Award,
  Target,
  ChevronRight,
  Briefcase,
  Heart,
  Clock,
  Check,
  ArrowRight,
  MapPin,
  Mail,
  Phone,
  Linkedin,
  ShieldCheck,
  Car,
  User,
  ChevronLeft,
} from "lucide-react";
import { ScrollAnimation } from "@/components/ui/ScrollAnimation";
import Marquee from "react-fast-marquee";
import {
  PropertyCard,
  type PropertyDetails,
} from "@/components/ui/PropertyCard";
import { getRandomLawFirmImage, getLawFirmImages } from "@/lib/unsplash";
import { AnimatedCounter } from "@/components/ui/animated-counter";

interface AboutSectionProps {
  dictionary: Dictionary;
  clientsDictionary?: Dictionary["clients"];
}

// Lawyer stats for AboutSection
const stats = [
  {
    icon: <Award className="h-8 w-8" />,
    value: 98,
    suffix: "%",
    key: "successRate" as keyof Dictionary["about"],
    fallback: "Success Rate",
    color: "from-green-500 to-green-600",
  },
  {
    icon: <Briefcase className="h-8 w-8" />,
    value: 15,
    suffix: "+",
    key: "yearsExperience" as keyof Dictionary["about"],
    fallback: "Years of Experience",
    color: "from-blue-500 to-blue-600",
  },
  {
    icon: <Check className="h-8 w-8" />,
    value: 350,
    suffix: "+",
    key: "casesWon" as keyof Dictionary["about"],
    fallback: "Cases Won",
    color: "from-purple-500 to-purple-600",
  },
  {
    icon: <Users className="h-8 w-8" />,
    value: 4,
    suffix: "",
    key: "languages" as keyof Dictionary["about"],
    fallback: "Languages Spoken",
    color: "from-pink-500 to-pink-600",
  },
];

// Law firm values
const getValues = (dictionary: Dictionary) => [
  {
    icon: <Target className="h-6 w-6" />,
    title:
      dictionary.about?.values?.clientOrientation?.title ||
      "Mandantenorientierung",
    description:
      dictionary.about?.values?.clientOrientation?.description ||
      "Jeder Fall ist einzigartig – wir bieten individuelle, persönliche Beratung und setzen uns engagiert für Ihre Rechte ein.",
  },
  {
    icon: <Award className="h-6 w-6" />,
    title: dictionary.about?.values?.expertise?.title || "Fachkompetenz",
    description:
      dictionary.about?.values?.expertise?.description ||
      "Langjährige Erfahrung und kontinuierliche Weiterbildung garantieren höchste juristische Qualität.",
  },
  {
    icon: <Check className="h-6 w-6" />,
    title: dictionary.about?.values?.integrity?.title || "Integrität",
    description:
      dictionary.about?.values?.integrity?.description ||
      "Transparenz, Ehrlichkeit und Diskretion sind die Grundpfeiler unserer Arbeit.",
  },
  {
    icon: <Heart className="h-6 w-6" />,
    title: dictionary.about?.values?.commitment?.title || "Engagement",
    description:
      dictionary.about?.values?.commitment?.description ||
      "Wir kämpfen leidenschaftlich und zielstrebig für die Interessen unserer Mandanten.",
  },
];

// Law firm team members
const getTeamMembers = (dictionary: Dictionary) => [
  {
    name: dictionary.about?.team?.lawyer1?.name || "Dr. Thomas Müller",
    role:
      dictionary.about?.team?.lawyer1?.role ||
      "Gründungspartner & Fachanwalt für Familienrecht",
    bio:
      dictionary.about?.team?.lawyer1?.bio ||
      "Dr. Thomas Müller ist Gründungspartner unserer Kanzlei und Experte für Familien- und Erbrecht. Mit über 20 Jahren Erfahrung und einer Erfolgsquote von 96% vertritt er Mandanten in komplexen Scheidungs- und Sorgerechtsverfahren. Als Dozent an der Universität München gibt er sein Wissen an die nächste Generation von Juristen weiter.",
    image:
      "https://images.unsplash.com/photo-1662104935741-3feec65ddf3f?auto=format&fit=crop&w=774&q=80",
    social: {
      linkedin: "#",
      email: "<EMAIL>",
    },
    stats: {
      successRate: 96,
      yearsExperience: 20,
      casesWon: 520,
      languages: ["Deutsch", "Englisch", "Französisch"],
      publications: 15,
      certifications: ["Fachanwalt für Familienrecht", "Mediator", "Dozent"],
    },
    expertise: [
      dictionary.about?.team?.lawyer1?.expertise?.divorce || "Scheidungsrecht",
      dictionary.about?.team?.lawyer1?.expertise?.custody || "Sorgerecht",
      dictionary.about?.team?.lawyer1?.expertise?.maintenance ||
        "Unterhaltsrecht",
      dictionary.about?.team?.lawyer1?.expertise?.assets ||
        "Vermögensauseinandersetzung",
    ],
  },
  {
    name: dictionary.about?.team?.lawyer2?.name || "Dr. Julia Schmidt",
    role:
      dictionary.about?.team?.lawyer2?.role ||
      "Partnerin & Fachanwältin für Arbeitsrecht",
    bio:
      dictionary.about?.team?.lawyer2?.bio ||
      "Dr. Julia Schmidt ist spezialisiert auf Arbeits- und Vertragsrecht mit besonderem Fokus auf Kündigungsschutz und Arbeitnehmerrechte. Sie vertritt sowohl Arbeitnehmer als auch mittelständische Unternehmen und bringt 15 Jahre Erfahrung in komplexen arbeitsrechtlichen Verfahren mit.",
    image:
      "https://images.unsplash.com/photo-1551836022-deb4988cc6c0?auto=format&fit=crop&w=774&q=80",
    social: {
      linkedin: "#",
      email: "<EMAIL>",
    },
    stats: {
      successRate: 94,
      yearsExperience: 15,
      casesWon: 380,
      languages: ["Deutsch", "Englisch", "Spanisch"],
    },
    expertise: [
      dictionary.about?.team?.lawyer2?.expertise?.dismissal ||
        "Kündigungsschutz",
      dictionary.about?.team?.lawyer2?.expertise?.contracts ||
        "Arbeitsverträge",
      dictionary.about?.team?.lawyer2?.expertise?.severance || "Abfindungen",
      dictionary.about?.team?.lawyer2?.expertise?.workCouncil ||
        "Betriebsratsrecht",
    ],
  },
  {
    name: dictionary.about?.team?.lawyer3?.name || "Michael Weber",
    role:
      dictionary.about?.team?.lawyer3?.role ||
      "Partner & Fachanwalt für Strafrecht",
    bio:
      dictionary.about?.team?.lawyer3?.bio ||
      "Michael Weber ist Ihr Verteidiger in allen strafrechtlichen Angelegenheiten. Mit seiner analytischen Denkweise und umfassenden Erfahrung in der Strafverteidigung setzt er sich engagiert für die Rechte seiner Mandanten ein und hat zahlreiche Freisprüche erwirkt.",
    image:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=774&q=80",
    social: {
      linkedin: "#",
      email: "<EMAIL>",
    },
    stats: {
      successRate: 92,
      yearsExperience: 18,
      casesWon: 290,
      languages: ["Deutsch", "Englisch"],
    },
    expertise: [
      dictionary.about?.team?.lawyer3?.expertise?.economic ||
        "Wirtschaftsstrafrecht",
      dictionary.about?.team?.lawyer3?.expertise?.defense ||
        "Strafverteidigung",
      dictionary.about?.team?.lawyer3?.expertise?.juvenile ||
        "Jugendstrafrecht",
    ],
  },
  {
    name: dictionary.about?.team?.lawyer4?.name || "Katharina Bauer",
    role:
      dictionary.about?.team?.lawyer4?.role || "Fachanwältin für Verkehrsrecht",
    bio:
      dictionary.about?.team?.lawyer4?.bio ||
      "Katharina Bauer ist Ihre Expertin für Verkehrsrecht und Unfallregulierung. Sie vertritt Mandanten bei Bußgeldverfahren, Führerscheinentzug und Schadensersatzansprüchen nach Verkehrsunfällen mit einer beeindruckenden Erfolgsquote.",
    image:
      "https://images.unsplash.com/photo-1580489944761-15a19d654956?auto=format&fit=crop&w=774&q=80",
    social: {
      linkedin: "#",
      email: "<EMAIL>",
    },
    stats: {
      successRate: 95,
      yearsExperience: 12,
      casesWon: 310,
      languages: ["Deutsch", "Englisch", "Italienisch"],
    },
    expertise: [
      dictionary.about?.team?.lawyer4?.expertise?.accident ||
        "Unfallregulierung",
      dictionary.about?.team?.lawyer4?.expertise?.fines || "Bußgeldverfahren",
      dictionary.about?.team?.lawyer4?.expertise?.license ||
        "Führerscheinrecht",
    ],
  },
  {
    name: dictionary.about?.team?.lawyer5?.name || "Alexander Hoffmann",
    role:
      dictionary.about?.team?.lawyer5?.role || "Fachanwalt für Vertragsrecht",
    bio:
      dictionary.about?.team?.lawyer5?.bio ||
      "Alexander Hoffmann ist spezialisiert auf Vertrags- und Handelsrecht. Er berät Unternehmen bei der Gestaltung und Prüfung von Verträgen sowie bei der Durchsetzung vertraglicher Ansprüche mit einem klaren Fokus auf wirtschaftliche Lösungen.",
    image:
      "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?auto=format&fit=crop&w=774&q=80",
    social: {
      linkedin: "#",
      email: "<EMAIL>",
    },
    expertise: [
      dictionary.about?.team?.lawyer5?.expertise?.contractDesign ||
        "Vertragsgestaltung",
      dictionary.about?.team?.lawyer5?.expertise?.commercialLaw ||
        "Handelsrecht",
      dictionary.about?.team?.lawyer5?.expertise?.termsConditions ||
        "AGB-Recht",
    ],
  },
  {
    name: dictionary.about?.team?.lawyer6?.name || "Lisa Neumann",
    role: dictionary.about?.team?.lawyer6?.role || "Fachanwältin für Erbrecht",
    bio:
      dictionary.about?.team?.lawyer6?.bio ||
      "Lisa Neumann unterstützt Sie bei allen Fragen rund um Testament, Erbschaft und Nachfolgeplanung. Mit ihrer einfühlsamen Art begleitet sie Mandanten durch schwierige Zeiten und findet faire Lösungen bei erbrechtlichen Auseinandersetzungen.",
    image:
      "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?auto=format&fit=crop&w=774&q=80",
    social: {
      linkedin: "#",
      email: "<EMAIL>",
    },
    expertise: [
      dictionary.about?.team?.lawyer6?.expertise?.willCreation ||
        "Testamentsgestaltung",
      dictionary.about?.team?.lawyer6?.expertise?.inheritance ||
        "Erbauseinandersetzung",
      dictionary.about?.team?.lawyer6?.expertise?.compulsory ||
        "Pflichtteilsrecht",
    ],
  },
];

// Office images
const officeImages = [
  "https://images.unsplash.com/photo-1577415124269-fc1140a69e91?auto=format&fit=crop&w=1000&q=80", // Modern law office reception
  "https://images.unsplash.com/photo-1568992687947-868a62a9f521?auto=format&fit=crop&w=1000&q=80", // Law library
  "https://images.unsplash.com/photo-1575505586569-646b2ca898fc?auto=format&fit=crop&w=1000&q=80", // Conference room
  "https://images.unsplash.com/photo-1497215728101-856f4ea42174?auto=format&fit=crop&w=1000&q=80", // Office workspace
  "https://images.unsplash.com/photo-1507842217343-583bb7270b66?auto=format&fit=crop&w=1000&q=80", // Law books
  "https://images.unsplash.com/photo-1453749024858-4bca89bd9edc?auto=format&fit=crop&w=1000&q=80", // Courtroom
];

// Areas of expertise with detailed descriptions
const getExpertiseAreas = (dictionary: Dictionary) => [
  {
    title: dictionary.about?.expertise?.familyLaw?.title || "Familienrecht",
    description:
      dictionary.about?.expertise?.familyLaw?.description ||
      "Kompetente Beratung und Vertretung in allen familienrechtlichen Angelegenheiten, von Scheidung über Sorgerecht bis hin zu Unterhaltsfragen.",
    icon: Users,
  },
  {
    title: dictionary.about?.expertise?.employmentLaw?.title || "Arbeitsrecht",
    description:
      dictionary.about?.expertise?.employmentLaw?.description ||
      "Umfassende Unterstützung bei arbeitsrechtlichen Konflikten, Kündigungsschutz und Vertragsgestaltung für Arbeitnehmer und Arbeitgeber.",
    icon: Briefcase,
  },
  {
    title: dictionary.about?.expertise?.criminalLaw?.title || "Strafrecht",
    description:
      dictionary.about?.expertise?.criminalLaw?.description ||
      "Engagierte Strafverteidigung in allen Verfahrensstadien mit Fokus auf eine optimale Verteidigungsstrategie.",
    icon: ShieldCheck,
  },
  {
    title: dictionary.about?.expertise?.trafficLaw?.title || "Verkehrsrecht",
    description:
      dictionary.about?.expertise?.trafficLaw?.description ||
      "Durchsetzung Ihrer Rechte bei Verkehrsunfällen, Bußgeldverfahren und Führerscheinangelegenheiten.",
    icon: Car,
  },
];

export const AboutSection = ({ dictionary, clientsDictionary }: AboutSectionProps) => {
  // State for active tabs and animations
  const [activeTab, setActiveTab] = useState<"company" | "team">("company");

  // Animation hooks
  const [companyRef, companyInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  const [teamRef, teamInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  const [propertiesRef, propertiesInView] = useInView({
    // For the new Featured Properties section
    triggerOnce: true,
    threshold: 0.1,
  });
  const [officeImagesRef, officeImagesInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  const [statsRef, statsInView] = useInView({
    triggerOnce: true,
    threshold: 0.2,
  });

  // Responsive state
  const [isMobile, setIsMobile] = useState(false);

  // Check for mobile devices
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Get translatable content
  const values = getValues(dictionary);
  const teamMembers = getTeamMembers(dictionary);
  const expertiseAreas = getExpertiseAreas(dictionary);

  // Additional data for the main lawyer profile
  const mainLawyer = teamMembers[0]; // Using Dr. Thomas Müller as the main lawyer profile

  return (
    <Section
      className="py-12 sm:py-16 md:py-24 bg-gray-50 dark:bg-gray-900 overflow-hidden relative min-h-screen pb-40 mb-0"
      id="about"
    >
      {/* Modern background elements */}
      <div className="absolute top-20 right-0 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-blob"></div>
      <div className="absolute bottom-20 left-0 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-blob animation-delay-2000"></div>

      <div className="container mx-auto px-4 pt-12 sm:pt-16 md:pt-20">
        {" "}
        {/* Added padding top as image was removed */}
        {/* Section Header */}
        <div className="text-center mb-10 sm:mb-16 px-4 sm:px-0">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 text-gray-900 dark:text-white inline-block">
            {dictionary.about?.title || "Über Unsere Kanzlei"}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 max-w-3xl mx-auto text-base sm:text-lg">
            {dictionary.about?.description ||
              "Lernen Sie unsere Werte und Grundsätze kennen, die uns antreiben, professionelle Rechtslösungen für unsere Mandanten zu schaffen."}
          </p>
        </div>
        {/* Main Lawyer Profile and Values Section */}
        <div className="mb-16 sm:mb-20" ref={companyRef}>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12">
            {/* Main Lawyer Profile - Left Side */}
            <motion.div
              className="bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg"
              initial={{ opacity: 0, x: -30 }}
              animate={
                companyInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -30 }
              }
              transition={{ duration: 0.6 }}
            >
              <div className="relative h-96 sm:h-[450px] w-full">
                <Image
                  src={mainLawyer.image}
                  alt={`Porträt von ${mainLawyer.name}`}
                  fill
                  sizes="(max-width: 1024px) 100vw, 50vw"
                  className="object-cover object-center"
                  priority
                />
              </div>
              <div className="p-6 sm:p-8">
                <h3 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                  {mainLawyer.name}
                </h3>
                <p className="text-blue-600 font-medium mb-4">
                  {mainLawyer.role}
                </p>
                <p className="text-gray-600 mb-6">{mainLawyer.bio}</p>

                {/* Stats in a row */}
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
                  {mainLawyer.stats !== undefined && (
                    <>
                      <div className="text-center">
                        <div className="text-2xl sm:text-3xl font-bold text-blue-600 mb-1">
                          {mainLawyer.stats.yearsExperience}+
                        </div>
                        <div className="text-sm text-gray-600">
                          {dictionary.about?.experience || "Jahre Erfahrung"}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl sm:text-3xl font-bold text-blue-600 mb-1">
                          {mainLawyer.stats.successRate}%
                        </div>
                        <div className="text-sm text-gray-600">
                          {dictionary.about?.successRate || "Erfolgsquote"}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl sm:text-3xl font-bold text-blue-600 mb-1">
                          {mainLawyer.stats.casesWon}+
                        </div>
                        <div className="text-sm text-gray-600">
                          {dictionary.about?.casesWon || "Gewonnene Fälle"}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl sm:text-3xl font-bold text-blue-600 mb-1">
                          {mainLawyer.stats.publications}
                        </div>
                        <div className="text-sm text-gray-600">
                          {dictionary.about?.publications || "Publikationen"}
                        </div>
                      </div>
                    </>
                  )}
                </div>

                {/* Contact buttons */}
                <div className="flex justify-center sm:justify-start space-x-4">
                  <a
                    href={`mailto:${mainLawyer.social.email}`}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    {dictionary.contact?.writeUs || "Kontakt"}
                  </a>
                  <a
                    href="#team"
                    className="inline-flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white rounded-lg transition-colors duration-200"
                  >
                    <Users className="h-4 w-4 mr-2" />
                    {dictionary.about?.meetTeam || "Team kennenlernen"}
                  </a>
                </div>
              </div>
            </motion.div>

            {/* Values Cards - Right Side */}
            <div className="flex flex-col">
              <motion.div
                className="mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={
                  companyInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                }
                transition={{ duration: 0.5 }}
              >
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-3">
                  {dictionary.about?.vision || "Unsere Grundwerte"}
                </h2>
                <p className="text-gray-600">
                  {dictionary.about?.visionDesc ||
                    "Wir glauben an die Schaffung einer positiven Wirkung durch Exzellenz und Integrität, geleitet von unseren Grundprinzipien."}
                </p>
              </motion.div>

              {/* 2x2 Grid of Values */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 flex-grow">
                {values.slice(0, 4).map((value, index) => (
                  <motion.div
                    key={`value-${index}`}
                    className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col h-full"
                    initial={{ opacity: 0, y: 20 }}
                    animate={
                      companyInView
                        ? { opacity: 1, y: 0 }
                        : { opacity: 0, y: 20 }
                    }
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <div className="flex flex-col items-center mb-4 text-center">
                      <div className="bg-blue-100 p-4 rounded-full mb-3">
                        {React.cloneElement(value.icon, {
                          className: "w-6 h-6 text-blue-600",
                        })}
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">
                        {value.title}
                      </h4>
                    </div>
                    <p className="text-sm text-gray-600 flex-grow text-center">
                      {value.description}
                    </p>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
        {/* Team Section */}
        <div className="mb-16 sm:mb-20 md:mb-24" ref={teamRef}>
          <motion.div
            className="text-center mb-10 sm:mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={teamInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-800 dark:text-white mb-3">
              {dictionary.about?.founderTitle || "Meet Our Expert Team"}
            </h2>
            <p className="text-base sm:text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              {dictionary.about?.founderDesc ||
                "Our team of dedicated professionals brings together diverse expertise to deliver exceptional results in every real estate endeavor."}
            </p>
          </motion.div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={`team-${index}`}
                className="bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg hover:shadow-2xl group transition-all duration-300 ease-in-out transform hover:-translate-y-1"
                initial={{ opacity: 0, y: 20 }}
                animate={
                  teamInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                }
                transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
              >
                <div className="relative h-60 sm:h-72 w-full">
                  <Image
                    src={member.image}
                    alt={`Porträt von ${member.name}`}
                    fill
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-5 sm:p-6 text-center">
                  <h4 className="text-xl sm:text-2xl font-semibold text-gray-800 dark:text-white mb-1">
                    {member.name}
                  </h4>
                  <p className="text-primary dark:text-secondary text-sm sm:text-base font-medium mb-3">
                    {member.role}
                  </p>
                  <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mb-4 h-16 sm:h-20 overflow-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
                    {member.bio}
                  </p>
                  <div className="flex justify-center space-x-2 sm:space-x-3">
                    {member.social.email && (
                      <a
                        href={`mailto:${member.social.email}`}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        <Mail className="h-4 w-4" />
                      </a>
                    )}
                    {member.social.linkedin && (
                      <a
                        href={member.social.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800"
                      >
                        <Linkedin className="h-4 w-4" />
                      </a>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </Section>
  );
};