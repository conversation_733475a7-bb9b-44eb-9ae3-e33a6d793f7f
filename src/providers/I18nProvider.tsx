'use client'

import { ReactNode, createContext, useContext, useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { getDictionary } from '@/lib/dictionaries'

export type Locale = 'en' | 'de' | 'ru' | 'tr' | 'ar'

interface I18nContextProps {
  locale: Locale
  setLocale: (locale: Locale) => void
  t: (key: string) => string
  dir: 'ltr' | 'rtl'
}

const defaultLocale: Locale = 'en'
const supportedLocales: Locale[] = ['en', 'de', 'ru', 'tr', 'ar']

// Create a context
const I18nContext = createContext<I18nContextProps>({
  locale: defaultLocale,
  setLocale: () => {},
  t: (key: string) => key,
  dir: 'ltr'
})

// Hook to use the i18n context
export const useI18n = () => useContext(I18nContext)

// Type for the translation dictionary
type TranslationDictionary = Record<string, any>

// Cache for dictionaries to avoid repeated fetch
const dictionaryCache: Record<string, TranslationDictionary> = {}

export function I18nProvider({ children }: { children: ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>(defaultLocale)
  const [translations, setTranslations] = useState<TranslationDictionary>({})
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  // Handle RTL direction for Arabic
  const dir = locale === 'ar' ? 'rtl' : 'ltr'

  // Effect to initialize locale from URL or local storage
  useEffect(() => {
    // Extract locale from the current URL path if available
    const pathLocale = pathname?.split('/')[1] as Locale
    if (pathLocale && supportedLocales.includes(pathLocale)) {
      setLocaleState(pathLocale)
      localStorage.setItem('locale', pathLocale)
      return
    }
    
    // If no locale in URL, check localStorage
    const savedLocale = localStorage.getItem('locale') as Locale
    if (savedLocale && supportedLocales.includes(savedLocale)) {
      setLocaleState(savedLocale)
    } else {
      // Try to detect user's language
      const browserLang = navigator.language.split('-')[0] as Locale
      const detectedLocale = supportedLocales.includes(browserLang) ? browserLang : defaultLocale
      setLocaleState(detectedLocale)
      localStorage.setItem('locale', detectedLocale)
    }
  }, [pathname])

  // Load translations when locale changes
  useEffect(() => {
    if (!locale) return

    async function loadTranslations() {
      try {
        setLoading(true)
        
        // Check cache first
        if (dictionaryCache[locale]) {
          setTranslations(dictionaryCache[locale])
          setLoading(false)
          return
        }
        
        // Fetch new dictionary
        const dict = await getDictionary(locale)
        dictionaryCache[locale] = dict
        setTranslations(dict)
      } catch (error) {
        console.error(`Failed to load translations for ${locale}:`, error)
        // Fallback to English if translation fails
        if (locale !== 'en') {
          try {
            const fallbackDict = await getDictionary('en')
            dictionaryCache['en'] = fallbackDict
            setTranslations(fallbackDict)
          } catch (e) {
            console.error('Failed to load fallback English dictionary:', e)
          }
        }
      } finally {
        setLoading(false)
      }
    }

    loadTranslations()
    
    // Update html lang attribute and dir attribute
    document.documentElement.lang = locale
    document.documentElement.dir = dir
  }, [locale, dir])

  // Function to change locale
  const setLocale = (newLocale: Locale) => {
    if (supportedLocales.includes(newLocale)) {
      setLocaleState(newLocale)
      localStorage.setItem('locale', newLocale)
      
      // Update URL to reflect locale change
      const segments = pathname?.split('/') || []
      if (segments.length > 1 && supportedLocales.includes(segments[1] as Locale)) {
        segments[1] = newLocale
        const newPath = segments.join('/')
        router.push(newPath)
      } else {
        router.push(`/${newLocale}${pathname}`)
      }
    }
  }

  // Translation function
  const t = (key: string) => {
    if (loading) return key

    // Handle nested keys like "nav.home"
    const keys = key.split('.')
    let value = translations

    for (const k of keys) {
      value = value?.[k]
      if (value === undefined) return key
    }

    return typeof value === 'string' ? value : key
  }

  return (
    <I18nContext.Provider value={{ locale, setLocale, t, dir }}>
      {children}
    </I18nContext.Provider>
  )
} 