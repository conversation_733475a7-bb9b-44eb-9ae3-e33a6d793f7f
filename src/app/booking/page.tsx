import { Metadata } from "next";
import { getDictionary } from "@/lib/dictionaries";
import { Locale } from "@/lib/dictionary";
import BookingCalendar from "@/components/booking/BookingCalendar";
import { FloatingContact } from "@/components/ui/FloatingContact";
import { FooterSection } from "@/components/sections/FooterSection";

export const metadata: Metadata = {
  title: "Book an Appointment | Your Business",
  description: "Schedule a consultation with our team to discuss your needs and how we can help you achieve your goals.",
};

export default async function BookingPage({
  params: { lang = "en" },
}: {
  params: { lang: Locale };
}) {
  const dictionary = await getDictionary(lang);

  return (
    <>
      <main className="flex-grow">
        <div className="pt-20 md:pt-24 lg:pt-28">
          <BookingCalendar
            dictionary={{
              title: "Book an Appointment",
              subtitle: "Schedule a meeting with our team",
              description: "Choose the type of meeting and duration that works best for you.",
              bookingButton: "Book an Appointment",
              successMessage: "Booking Request Submitted!",
              backToCalendar: "Book Another Meeting",
              bookingTypes: {
                consultation: "Initial Consultation",
                meeting: "Project Discussion",
                support: "Support Session",
              },
              durations: {
                short: "15 minutes",
                medium: "30 minutes",
                long: "60 minutes",
              },
              calendarButton: "Select Date",
              submitButton: "Submit Booking Request",
              backButton: "Back",
              timeSlots: "View Available Times",
              contactInfo: "Enter Contact Info",
              nameLabel: "Your Name",
              emailLabel: "Email Address",
              phoneLabel: "Phone Number",
              messageLabel: "Additional Information"
            }}
          />
        </div>
      </main>
      <FloatingContact dictionary={dictionary.contact} />
      <FooterSection dictionary={dictionary.footer} />
    </>
  );
}
