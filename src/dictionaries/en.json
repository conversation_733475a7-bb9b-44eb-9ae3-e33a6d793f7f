{"nav": {"home": "Home", "about": "About", "solutions": "Solutions", "services": "Services", "techStack": "Tech Stack", "portfolio": "Portfolio", "contact": "Contact", "chatWithUs": "Chat With Us", "bookConsultation": "Book a Consultation", "apps": "Products", "pricing": "Pricing", "prices": "Prices", "calculator": "Pricing Calculator", "clients": "Clients", "testimonials": "Testimonials"}, "hero": {"title": "Your Trusted Legal Partner", "subtitle": "Expert legal advice for individuals and businesses", "tagline": "Competence. Integrity. Results.", "description": "Rely on our experienced legal team for comprehensive advice and effective representation. Whether you need support in family, business, or criminal law, we are at your side.", "typing": {"sequence1": "Luxury apartments in prime locations.", "sequence2": "Family homes with space to grow.", "sequence3": "Investment properties with strong returns."}, "cta": {"primary": "Contact Us", "secondary": "Book a Consultation", "calculator": "Get Price Estimate"}, "metrics": {"development": {"label": "Faster Development", "value": 40, "description": "40% faster development through\nefficient processes & modern tech."}, "timeToMarket": {"label": "Faster Time-to-Market", "value": 50, "description": "50% quicker market entry with\nstreamlined workflow & deployment."}, "costSaving": {"label": "Cost Saving", "value": 30, "description": "30% cost reduction through\noptimized development & resources."}}}, "about": {"title": "About Us", "subtitle": "Your Real Estate Partner", "description": "With years of experience in the real estate market, our team of dedicated professionals is committed to helping you find the perfect property or sell your current one at the best possible price.", "vision": "Vision", "visionDesc": "To be the most trusted name in real estate, known for our integrity, expertise, and commitment to exceeding client expectations in every transaction.", "mission": "Mission", "missionDesc": "We strive to provide exceptional real estate services by combining deep market knowledge, personalized attention, and innovative marketing strategies to achieve the best possible outcomes for our clients.", "founderTitle": "Our Team", "founderDesc": "Our team consists of experienced professionals from various backgrounds, dedicated to creating exceptional digital experiences for our clients.", "skills": "Expertise", "projects": "Projects", "testimonials": "Testimonials", "experience": "Years Experience", "clients": "Happy Clients", "transformBusiness": "Transforming businesses through technology", "createSolutions": "Creating future-proof digital solutions", "stayingAhead": "Staying at the forefront of technology", "exceptionalUX": "Delivering exceptional user experiences", "highPerformance": "Building high-performance websites", "solvingChallenges": "Solving real business challenges with technology", "flutterExpert": "Expert", "webDevAdvanced": "Advanced", "aiIntegration": "Integration", "successRate": "Success Rate", "casesWon": "Cases Won", "publications": "Publications", "meetTeam": "Meet Our Team", "values": {"clientOrientation": {"title": "Client Orientation", "description": "Every case is unique - we offer individual, personal advice and are committed to your rights."}, "expertise": {"title": "Professional Expertise", "description": "Many years of experience and continuous education guarantee the highest legal quality."}, "integrity": {"title": "Integrity", "description": "Transparency, honesty, and discretion are the cornerstones of our work."}, "commitment": {"title": "Commitment", "description": "We fight passionately and purposefully for the interests of our clients."}}, "team": {"lawyer1": {"name": "Dr. <PERSON>", "role": "Founding Partner & Family Law Specialist", "bio": "Dr. <PERSON> is the founding partner of our law firm and an expert in family and inheritance law. With over 20 years of experience and a success rate of 96%, he represents clients in complex divorce and custody proceedings. As a lecturer at the University of Munich, he passes on his knowledge to the next generation of lawyers.", "expertise": {"divorce": "Divorce Law", "custody": "Custody Law", "maintenance": "Maintenance Law", "assets": "Asset Division"}}, "lawyer2": {"name": "Dr. <PERSON>", "role": "Partner & Employment Law Specialist", "bio": "Dr. <PERSON> specializes in employment and contract law with a particular focus on protection against dismissal and employee rights. She represents both employees and medium-sized companies and brings 15 years of experience in complex employment law proceedings.", "expertise": {"dismissal": "Dismissal Protection", "contracts": "Employment Contracts", "severance": "Severance Packages", "workCouncil": "Works Council Law"}}, "lawyer3": {"name": "<PERSON>", "role": "Partner & Criminal Law Specialist", "bio": "<PERSON> is your defender in all criminal matters. With his analytical thinking and comprehensive experience in criminal defense, he is committed to the rights of his clients and has achieved numerous acquittals.", "expertise": {"economic": "Economic Criminal Law", "defense": "Criminal Defense", "juvenile": "Juvenile Criminal Law"}}, "lawyer4": {"name": "<PERSON><PERSON><PERSON>", "role": "Traffic Law Specialist", "bio": "<PERSON><PERSON><PERSON> is your expert for traffic law and accident regulation. She represents clients in fine proceedings, driver's license revocation, and claims for damages after traffic accidents with an impressive success rate.", "expertise": {"accident": "Accident Settlement", "fines": "Fine Proceedings", "license": "Driver's License Law"}}, "lawyer5": {"name": "<PERSON>", "role": "Contract Law Specialist", "bio": "<PERSON> specializes in contract and commercial law. He advises companies on the drafting and review of contracts and the enforcement of contractual claims with a clear focus on economic solutions.", "expertise": {"contractDesign": "Contract Design", "commercialLaw": "Commercial Law", "termsConditions": "Terms & Conditions Law"}}, "lawyer6": {"name": "<PERSON>", "role": "Inheritance Law Specialist", "bio": "<PERSON> supports you in all matters relating to wills, inheritance, and succession planning. With her empathetic manner, she accompanies clients through difficult times and finds fair solutions in inheritance disputes.", "expertise": {"willCreation": "Will Creation", "inheritance": "Inheritance Disputes", "compulsory": "Compulsory Portion Law"}}}, "expertise": {"familyLaw": {"title": "Family Law", "description": "Competent advice and representation in all family law matters, from divorce and custody to maintenance issues."}, "employmentLaw": {"title": "Employment Law", "description": "Comprehensive support for employment law conflicts, protection against dismissal, and contract design for employees and employers."}, "criminalLaw": {"title": "Criminal Law", "description": "Committed criminal defense at all stages of proceedings with a focus on an optimal defense strategy."}, "trafficLaw": {"title": "Traffic Law", "description": "Enforcement of your rights in traffic accidents, fine proceedings, and driver's license matters."}}}, "advantages": {"title": "OUR SOLUTIONS", "subtitle": "How We Help You Build Successful Mobile Apps", "description": "We leverage cutting-edge technologies across multiple domains to deliver robust and scalable solutions for your business needs.", "speed": "Rapid Development", "speedDesc": "We deliver solutions quickly without compromising on quality", "stability": "Reliable Applications", "stabilityDesc": "Our applications are built for stability and performance", "cost": "Cost Efficiency", "costDesc": "Optimized development process saves you time and money", "timeToMarket": "Faster Time-to-Market", "timeToMarketDesc": "Launch your product quickly and stay ahead of competition", "aiIntegration": "AI Integration", "aiIntegrationDesc": "Enhance your business with powerful AI capabilities", "development": "Full-Stack Development", "developmentTime": "4-12 weeks, varies with complexity", "developmentDesc": "Complete mobile application development from concept to deployment, with full backend integration and advanced features.", "mvp": "MVP Development", "mvpTime": "2-4 weeks, varies with complexity", "mvpDesc": "Launch quickly with a Minimum Viable Product featuring core functionality to validate your concept and attract early users or investors.", "prototype": "Rapid Prototyping", "prototypeTime": "1-2 weeks, varies with complexity", "prototypeDesc": "Test concepts quickly with interactive prototypes before committing to full development, saving time and resources.", "qa": "Quality Assurance", "qaTime": "Ongoing, scales with project complexity", "qaDesc": "Comprehensive testing across devices and platforms to ensure your app performs flawlessly with automated and manual testing protocols.", "consulting": "Technical Consulting", "consultingTime": "As needed, based on project complexity", "consultingDesc": "Expert advice on technology stack, architecture decisions, and implementation strategies to optimize your mobile application.", "uiux": "UI/UX Design", "uiuxTime": "2-3 weeks, varies with complexity", "uiuxDesc": "User-centered design that balances aesthetics with functionality, creating intuitive and engaging mobile experiences.", "maintenance": "Maintenance & Support", "maintenanceTime": "Ongoing, scales with project complexity", "maintenanceDesc": "Long-term support with regular updates, performance optimization, and feature enhancements to keep your app competitive.", "analytics": "Analytics Integration", "analyticsTime": "1-2 weeks, varies with complexity", "analyticsDesc": "Data tracking implementation to gain actionable insights into user behavior, enabling data-driven decisions for your app.", "training": "Team Training", "trainingTime": "1-2 weeks, varies with complexity", "trainingDesc": "Comprehensive training for your team on maintaining and extending your application after handover.", "developmentEfficiency": "Development Efficiency", "timeToMarketReduction": "Time-to-Market Reduction", "conceptValidation": "Concept Validation", "bugFreeRate": "Bug-Free Rate", "technicalImprovement": "Technical Improvement", "userSatisfaction": "User Satisfaction", "appUptime": "App Uptime", "dataAccuracy": "Data Accuracy", "knowledgeRetention": "Knowledge Retention", "developmentInfo": {"title": "Development Timeframes", "simpleApp": {"title": "Simple App", "examples": "Examples: To-do lists, calculators, simple information apps without backend.", "features": ["Few screens (3-5)", "No or minimal backend integration", "Standard UI components", "No complex animations or functions"], "timeline": {"total": "Development time: 4-8 weeks", "frontend": "Frontend: 2-4 weeks", "backend": "Backend (if needed): 1-2 weeks", "testing": "Testing and deployment: 1-2 weeks"}}, "mediumApp": {"title": "Medium App", "examples": "Examples: E-commerce apps, social media apps with basic features, apps with user registration and database integration.", "features": ["6-15 screens", "Backend integration (e.g., REST or GraphQL APIs)", "User registration and authentication", "Database for user and app data", "Some animations and interactive elements", "Push notifications"], "timeline": {"total": "Development time: 8-16 weeks", "frontend": "Frontend: 4-6 weeks", "backend": "Backend: 3-5 weeks", "testing": "Testing and deployment: 2-3 weeks"}}, "complexApp": {"title": "Complex App", "examples": "Examples: Apps like Uber, Instagram, or banking apps with advanced features.", "features": ["15+ screens", "Highly interactive user interface", "Real-time features (e.g., live tracking, chat)", "Third-party API integration (e.g., payment gateways, card APIs)", "Scalable backend with cloud integration", "Security features (e.g., encryption, two-factor authentication)", "Offline functionality"], "timeline": {"total": "Development time: 16-32 weeks or longer", "frontend": "Frontend: 6-10 weeks", "backend": "Backend: 6-12 weeks", "testing": "Testing and deployment: 4-6 weeks"}}, "factors": {"title": "Factors Affecting Development Time", "teamSize": "Team size: A larger team (e.g., separate developers for frontend, backend, and QA) can speed up development. A single developer needs more time.", "technology": "Technology: Native development (e.g., Swift for iOS, <PERSON><PERSON>in for Android) often takes longer than cross-platform approaches like Flutter or React Native. Cross-platform frameworks can reduce development time by 30-40%.", "requirements": "Requirements and changes: Frequent changes or unclear requirements can extend development time.", "testing": "Testing and debugging: Complex apps require more time for testing, especially on multiple platforms (iOS and Android).", "design": "Design: Simple designs require less time, while custom, animated designs increase development time."}, "summary": "Summary: Simple App: 4-8 weeks. Medium App: 8-16 weeks. Complex App: 16-32 weeks or longer.", "aiComparison": "With our AI-powered development and Flutter, we can reduce these timelines by 40-60% while maintaining high quality and performance."}}, "services": {"title": "Our Technology Stack", "subtitle": "Comprehensive Digital Solutions", "description": "We leverage cutting-edge technologies across multiple domains to deliver robust and scalable solutions for your business needs.", "frontend": "Frontend Development", "frontendDesc": "Building responsive and interactive user interfaces with modern web technologies", "backend": "Backend Development", "backendDesc": "Creating robust server-side solutions and APIs for scalable applications", "mobile": "Mobile Development", "mobileDesc": "Developing cross-platform mobile applications with native performance", "ai": "AI & Machine Learning", "aiDesc": "Integrating intelligent features and automation into applications", "mobileApps": "Mobile App Development", "mobileAppsDesc": "Cross-platform applications with Flutter for iOS and Android", "webDev": "Web Development", "webDevDesc": "Modern, responsive websites and web applications", "uiuxDesign": "UI/UX Design", "uiuxDesignDesc": "Intuitive, user-focused design that delights customers", "consulting": "Technical Consulting", "consultingDesc": "Expert advice on technology strategy and implementation", "aiSolutions": "AI Integration", "aiSolutionsDesc": "Incorporate AI capabilities to enhance your business", "viewAll": "View All Services"}, "serviceSection": {"title": "Our Services", "subtitle": "Comprehensive Real Estate Solutions", "description": "From finding your dream home to selling your property at premium value, we offer a full spectrum of real estate services designed to meet your unique needs and exceed your expectations.", "viewAll": "Discuss Your Project", "comparisonTitle": "Why Choose Us?", "comparisonSubtitle": "See how we compare to traditional approaches", "timeComparison": {"title": "Time", "traditional": "16 weeks", "withUs": "10 weeks", "savings": "40%"}, "costComparison": {"title": "Cost", "traditional": "€50,000", "withUs": "€25,000", "savings": "50%"}, "qualityComparison": {"title": "Quality", "traditional": "Standard", "withUs": "Premium", "savings": "35% better"}, "service1": {"title": "Residential Sales", "description": "Find your dream home or sell your property with our expert residential sales service. We handle everything from listing to closing with professionalism and care.", "items": [{"name": "Home Buying", "description": "Personalized property searches based on your specific needs and preferences"}, {"name": "Home Selling", "description": "Strategic pricing, professional photography, and targeted marketing to attract qualified buyers"}, {"name": "Market Analysis", "description": "Detailed property valuations and market trend analysis to maximize your investment"}, {"name": "Negotiation", "description": "Expert negotiation to secure the best possible terms and price for your transaction"}]}, "service2": {"title": "Rental Services", "description": "Whether you're looking to rent a property or need a reliable tenant for your investment, our rental services provide comprehensive solutions for landlords and tenants alike.", "items": [{"name": "Property Listing", "description": "Strategic marketing of your rental property across multiple platforms to attract qualified tenants"}, {"name": "Tenant Screening", "description": "Thorough background checks and qualification process to find reliable tenants"}, {"name": "Rental Management", "description": "Ongoing management services for property owners, including maintenance coordination and rent collection"}, {"name": "Tenant Placement", "description": "Finding the perfect rental home that meets your budget, location preferences, and lifestyle needs"}]}, "service3": {"title": "Luxury Properties", "description": "Experience the pinnacle of real estate with our exclusive luxury property services, designed for discerning clients seeking exceptional homes and investment opportunities.", "items": [{"name": "Premium Listings", "description": "Access to exclusive high-end properties, often before they reach the public market"}, {"name": "Personalized Service", "description": "Dedicated luxury property specialists providing discreet, white-glove service throughout your journey"}, {"name": "Global Network", "description": "Connections with international buyers and sellers through our extensive global partner network"}, {"name": "Investment Advisory", "description": "Expert guidance on luxury property investments for portfolio diversification and wealth preservation"}]}, "service4": {"title": "Financing Consultation", "description": "Navigate the complex world of real estate financing with our expert consultation services, helping you secure the best mortgage rates and terms for your property purchase.", "items": [{"name": "Mortgage Options", "description": "Comprehensive overview of available financing options tailored to your financial situation"}, {"name": "Rate Comparison", "description": "Side-by-side analysis of rates and terms from multiple lenders to find the best deal"}, {"name": "Pre-Approval", "description": "Assistance with mortgage pre-approval to strengthen your negotiating position when making offers"}, {"name": "Financial Planning", "description": "Long-term financial planning for real estate investments and property portfolio management"}]}, "service5": {"title": "Commercial Properties", "description": "Specialized expertise in commercial real estate transactions, helping businesses find the perfect location for their operations or investors maximize returns on commercial properties.", "items": [{"name": "Office Spaces", "description": "Finding the ideal office space that balances location, amenities, and budget for your business needs"}, {"name": "Retail Locations", "description": "Strategic site selection for retail businesses to maximize foot traffic and customer engagement"}, {"name": "Industrial Properties", "description": "Sourcing warehouses, manufacturing facilities, and distribution centers with optimal logistics considerations"}, {"name": "Investment Properties", "description": "Analysis and acquisition of income-producing commercial properties with strong ROI potential"}]}, "service6": {"title": "Property Management", "description": "Comprehensive property management services for investors and landlords, handling every aspect of rental property ownership to maximize returns while minimizing stress.", "items": [{"name": "Tenant Management", "description": "Complete tenant lifecycle management from screening to move-out inspections"}, {"name": "Maintenance Coordination", "description": "Prompt handling of repairs and maintenance with our network of trusted contractors"}, {"name": "Financial Reporting", "description": "Regular financial statements and comprehensive reporting on property performance"}, {"name": "Rent Collection", "description": "Reliable rent collection systems with online payment options and follow-up on late payments"}]}}, "solutionsPortfolio": {"title": "Featured Properties", "subtitle": "Exceptional Real Estate Opportunities", "description": "Browse our selection of premium properties currently available on the market. Each listing represents an exceptional opportunity for homebuyers and investors alike.", "clickInstruction": "Click property for details & more photos", "imageCounter": "Image {current} of {total}", "keyFeatures": "Key Features", "problemsSolved": "Problems Solved", "viewDetails": "View Details", "screenshot": "Screenshot", "screenshots": "Screenshots", "categories": {"all": "All Categories", "luxuryApartment": "Luxury Apartment", "familyHome": "Family Home"}, "items": {"luxuryApartment": {"title": "Luxury Penthouse - Downtown", "description": "Stunning penthouse apartment with panoramic city views and premium finishes throughout.", "features": ["3 Bedrooms, 2.5 Bathrooms", "180m² Living Space", "Private Rooftop Terrace", "Floor-to-Ceiling Windows"], "useCase": "Perfect for urban professionals or as a prestigious investment property", "technologies": ["Smart Home System", "Underfloor Heating", "Premium Kitchen Appliances", "Electric Car Charging"], "benefits": ["Prime Location", "Exceptional Build Quality", "Strong Rental Potential", "Secure Building with Concierge"]}, "familyHome": {"title": "Spacious Family Villa - Suburban", "description": "Beautiful detached family home in a peaceful suburban neighborhood with excellent schools nearby.", "features": ["4 Bedrooms, 3 Bathrooms", "240m² Living Space", "800m² Garden with Patio", "Double Garage"], "useCase": "Ideal for growing families looking for space and comfort in a family-friendly area", "technologies": ["Energy Efficient Construction", "Zoned Heating System", "Modern Security System", "Fiber Internet Ready"], "benefits": ["Excellent School District", "Quiet, Safe Neighborhood", "Plenty of Living Space", "Well-maintained Garden"], "problemsSolved": ["Organization", "Wellness Management", "Personal Development", "Life Balancing"]}, "automotive": {"title": "Automotive Technology Solutions", "description": "Digital interfaces and control systems for modern automotive applications.", "features": ["Vehicle Management", "Navigation Systems", "Diagnostic Tools", "Driver Assistance"], "problemsSolved": ["Vehicle Control", "Navigation Challenges", "Maintenance Tracking", "Driver Experience"]}}}, "properties": {"title": "Featured Properties", "subtitle": "Exclusive Real Estate Opportunities", "description": "Discover our handpicked selection of premium properties available for sale and rent in the most sought-after locations.", "viewAllButton": "View All Properties", "searchPlaceholder": "Search by location, features, or property type...", "filters": {"all": "All Properties", "forSale": "For Sale", "forRent": "For Rent", "priceRange": "Price Range", "bedrooms": "Bedrooms", "propertyType": "Property Type"}, "propertyTypes": {"house": "House", "apartment": "Apartment", "villa": "Villa", "penthouse": "Penthouse", "commercial": "Commercial", "land": "Land"}}, "portfolio": {"title": "Our Expertise", "subtitle": "Sectors & Problem Solving", "description": "Explore our expertise across various sectors and the problems we solve for businesses.", "all": "All Sectors", "screenshot": "Screenshot", "screenshots": "Screenshots", "problemsWeSolve": "Problems We Solve", "noSectorsFound": "No sectors found for the selected filter.", "categories": {"aiAssistant": "AI Assistant", "foodDelivery": "Food Delivery", "hospitality": "Hospitality", "medical": "Medical", "lifestyle": "Lifestyle Apps", "automotive": "Automotive"}, "sectors": {"assistant": "Assistant Apps", "food": "Food Order & Delivery", "hospitality": "Hospitality", "lifestyle": "Lifestyle Apps", "social": "Social Media", "automotive": "Automotive", "medical": "Medical & Healthcare", "business": "Business Solutions"}, "sectorDescriptions": {"assistant": "AI-powered assistants that enhance productivity and provide personalized recommendations", "food": "Seamless food ordering and delivery platforms with real-time tracking", "hospitality": "Digital solutions for hotels and hospitality businesses to enhance guest experience", "lifestyle": "Applications that enhance daily life, wellness, and personal development", "social": "Platforms that connect people and communities through shared interests", "automotive": "Smart solutions for vehicle management, navigation, and driver assistance", "medical": "Digital health solutions that improve patient care and medical operations", "business": "Enterprise applications that streamline operations and boost productivity"}, "problems": {"assistant": {"1": "Information overload", "2": "Task management", "3": "Decision making support"}, "food": {"1": "Order management", "2": "Delivery logistics", "3": "Restaurant discovery"}, "hospitality": {"1": "Guest management", "2": "Service optimization", "3": "Booking systems"}, "lifestyle": {"1": "Health tracking", "2": "Habit formation", "3": "Personal organization"}, "social": {"1": "User engagement", "2": "Content discovery", "3": "Community building"}, "automotive": {"1": "Vehicle monitoring", "2": "Navigation optimization", "3": "Driver experience"}, "medical": {"1": "Patient management", "2": "Health monitoring", "3": "Medical record systems"}, "business": {"1": "Workflow optimization", "2": "Data management", "3": "Team collaboration"}}, "viewDetails": "View Details", "viewAllProjects": "View All Projects", "features": "Features", "featureItem1": "Intuitive user interface", "featureItem2": "Multi-platform support", "featureItem3": "Advanced data analytics", "featureItem4": "Real-time notifications", "technologies": "Technologies Used", "requestDemo": "Request a Demo"}, "clients": {"title": "Our Clients", "subtitle": "Companies We've Worked With", "description": "We've had the pleasure of working with a diverse range of clients across various industries.", "visitWebsite": "Visit Website"}, "testimonials": {"title": "What Our Clients Say", "subtitle": "Client Testimonials", "description": "Read what our valued clients have to say about their experience working with us and the results we've delivered.", "readMore": "Read more", "readLess": "Read less", "formTitle": "Share Your Experience", "name": "Name", "email": "Email", "yourName": "Your name", "yourEmail": "Your email", "caseType": "Legal Area", "rating": "Rating", "stars": "Stars", "yourExperience": "Your Experience", "experienceDescription": "Describe your experience with our law firm...", "submitReview": "Submit Review", "selectOption": "Please select", "familyLaw": "Family Law", "employmentLaw": "Employment Law", "criminalLaw": "Criminal Law", "trafficLaw": "Traffic Law", "contractLaw": "Contract Law"}, "contact": {"title": "Contact Us", "subtitle": "Get in Touch", "description": "Reach out to us for any inquiries, project discussions, or to schedule a consultation. We're here to help bring your digital vision to life.", "name": "Name", "email": "Email", "phone": "Phone", "message": "Message", "send": "Send Message", "yourName": "Your Name", "yourEmail": "Your Email", "subject": "Subject", "howCanIHelp": "How can I help?", "yourMessageHere": "Your message here", "getInTouch": "Get In Touch", "sendMessage": "Send a Message", "schedule": "Schedule a Call", "freeConsultation": "Book a free 15-min consultation", "location": "Location", "submitButton": "Send Message", "sending": "Sending...", "messageSent": "Message Sent!", "errorTryAgain": "Error, please try again", "orSchedule": "Or schedule a meeting directly using the calendar link", "whatsapp": "WhatsApp", "linkedin": "LinkedIn", "github": "GitHub", "booking": "Book Appointment", "directions": "Get Directions"}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Everything you need to know about our mobile app development and MVP services", "description": "Find answers to common questions about our mobile app development and MVP services. Can't find what you're looking for? Contact us directly.", "showMore": "Show More Questions", "showLess": "Show Less", "items": [{"id": "app-development", "question": "What services do you offer for mobile app development?", "answer": "We provide comprehensive mobile app development services including native iOS and Android development, cross-platform solutions using Flutter and React Native, UI/UX design, backend integration, and ongoing maintenance and support. Our expertise spans various industries and we specialize in creating scalable, high-performance mobile applications that deliver exceptional user experiences."}, {"id": "mvp-development", "question": "How do you approach MVP (Minimum Viable Product) development?", "answer": "Our MVP development process focuses on creating a functional product with core features that solve your target audience's key problems. We start with thorough market research and user analysis, define essential features, develop a streamlined product, and gather user feedback for iterative improvements. This approach helps validate your business idea quickly and cost-effectively before investing in a full-scale solution."}, {"id": "development-timeline", "question": "How long does it take to develop a mobile app?", "answer": "The timeline for mobile app development varies based on complexity, features, and platforms. A basic MVP can be developed in 2-3 months, while more complex applications may take 4-6 months or longer. We work with you to establish realistic timelines and milestones, ensuring transparent communication throughout the development process."}, {"id": "technology-stack", "question": "What technology stack do you use for app development?", "answer": "We utilize modern, robust technology stacks tailored to each project's specific requirements. For native development, we use Swift/SwiftUI for iOS and Kotlin/Java for Android. For cross-platform solutions, we leverage Flutter and React Native. Our backend technologies include Node.js, Python, Firebase, and AWS services. We select the optimal stack based on your project needs, performance requirements, and long-term scalability goals."}, {"id": "app-cost", "question": "How much does it cost to develop a mobile app?", "answer": "Mobile app development costs vary widely depending on complexity, features, platforms, and design requirements. Basic MVPs typically start from €15,000-30,000, while more complex applications with advanced features can range from €30,000-100,000+. We provide detailed estimates based on your specific requirements and offer flexible engagement models to accommodate different budget constraints."}]}, "footer": {"copyright": "© 2025 Company Name. All rights reserved.", "description": "We develop sophisticated mobile and web applications that transform businesses through innovative technology.", "quickLinks": "Quick Links", "footerContact": "Contact Us", "legal": "Legal", "newsletter": "Newsletter", "newsletterDesc": "Subscribe to our newsletter to receive updates and insights.", "emailPlaceholder": "Enter your email", "subscribe": "Subscribe", "builtWith": "Built with", "and": "and", "downloadCV": "My Resume", "englishCV": "English", "germanCV": "German", "contactUs": "Contact Us", "openingHours": "Opening Hours:", "weekdayHours": "Monday - Friday: 9:00 - 18:00", "weekendHours": "Weekend: Closed", "appointmentsNote": "Appointments outside office hours available by arrangement", "stayInformed": "Stay informed about legal developments", "legalUpdates": "Subscribe to our newsletter for current legal changes, case law, and expert tips.", "privacyNote": "We respect your privacy. Unsubscribe at any time."}, "cookies": {"title": "<PERSON><PERSON>", "description": "We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking \"Accept All\", you consent to our use of cookies.", "acceptAll": "Accept All", "decline": "Decline", "customize": "Customize", "necessary": "Necessary Cookies", "necessaryDesc": "These cookies are essential for the website to function properly and cannot be disabled.", "analytics": "Analytics Cookies", "analyticsDesc": "These cookies help us understand how visitors interact with our website and help us improve our services.", "marketing": "Marketing Cookies", "marketingDesc": "These cookies are used to track visitors across websites to display relevant advertisements.", "functional": "Functional Cookies", "functionalDesc": "These cookies enable enhanced functionality and personalization on our website.", "save": "Save Preferences", "settings": "<PERSON><PERSON>", "close": "Close", "cookiePolicy": "<PERSON><PERSON>", "privacyPolicy": "Privacy Policy"}, "heroParallax": {"title": "The Ultimate Development Studio", "subtitle": "We build beautiful products with the latest technologies and frameworks. We are a team of passionate developers and designers that love to build amazing products.", "products": {"mobileApp": "Mobile App Development", "webDev": "Web Development", "uiux": "UI/UX Design", "ecommerce": "E-commerce Solutions", "ai": "AI Integration", "cloud": "Cloud Solutions", "devops": "DevOps", "dataAnalytics": "Data Analytics", "blockchain": "Blockchain Development", "arvr": "AR/VR Solutions", "customSoftware": "Custom Software", "mobileGame": "Mobile Game Development", "iot": "IoT Solutions", "api": "API Development", "cybersecurity": "Cybersecurity"}}, "featuresSection": {"features": [{"title": "Built for developers", "description": "Built for engineers, developers, dreamers, thinkers and doers.", "icon": "IconTerminal2"}, {"title": "Ease of use", "description": "It's as easy as using an Apple, and as expensive as buying one.", "icon": "IconEaseInOut"}, {"title": "Pricing like no other", "description": "Our prices are best in the market. No cap, no lock, no credit card required.", "icon": "Icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "100% Uptime guarantee", "description": "We just cannot be taken down by anyone.", "icon": "IconCloud"}, {"title": "Multi-tenant Architecture", "description": "You can simply share passwords instead of buying new seats", "icon": "IconRouteAltLeft"}, {"title": "24/7 Customer Support", "description": "We are available a 100% of the time. Atleast our AI Agents are.", "icon": "IconHelp"}, {"title": "Money back guarantee", "description": "If you donot like EveryAI, we will convince you to like us.", "icon": "IconAdjustmentsBolt"}, {"title": "And everything else", "description": "I just ran out of copy ideas. Accept my sincere apologies", "icon": "IconHeart"}]}, "legalServices": {"familyLaw": {"title": "Family Law", "description": "Expert legal support for families navigating complex emotional situations", "divorce": {"title": "Divorce", "description": "Guidance through the dissolution of marriage with compassion"}, "maintenance": {"title": "Maintenance", "description": "Ensuring fair financial support for family members"}, "custody": {"title": "Custody & Visitation", "description": "Protecting the best interests of children in family disputes"}}, "employmentLaw": {"title": "Employment Law", "description": "Defending employee rights and supporting employers with compliance", "protection": {"title": "Employee Protection", "description": "Protection against wrongful termination and workplace harassment"}, "contract": {"title": "Employment Contracts", "description": "Drafting and reviewing fair employment agreements"}, "warning": {"title": "Warning Letters", "description": "Proper handling of workplace disciplinary procedures"}}, "contractLaw": {"title": "Contract Law", "description": "Expert drafting and dispute resolution for all types of contracts", "purchase": {"title": "Purchase Agreements", "description": "Secure and fair property and asset purchase contracts"}, "service": {"title": "Service Contracts", "description": "Clear and protective service agreements for businesses"}, "work": {"title": "Work Contracts", "description": "Comprehensive employment terms and conditions"}}, "criminalLaw": {"title": "Criminal Law", "description": "Strong defense and representation in criminal proceedings", "defense": {"title": "Criminal Defense", "description": "Vigorous defense against criminal allegations"}, "victimProtection": {"title": "Victim Protection", "description": "Support and representation for victims of crimes"}, "juvenile": {"title": "Juvenile Criminal Law", "description": "Specialized approach for young offenders"}}, "civilLaw": {"title": "Civil Law", "description": "Resolution of disputes between individuals and organizations", "damages": {"title": "Damages Claims", "description": "Recovery of damages for personal injury or property"}, "disputes": {"title": "Neighbor Disputes", "description": "Mediation and resolution of property-related conflicts"}, "general": {"title": "General Civil Cases", "description": "Representation in miscellaneous civil matters"}}, "trafficLaw": {"title": "Traffic Law", "description": "Expert assistance with all traffic-related legal matters", "accident": {"title": "Traffic Accidents", "description": "Legal support following vehicle collisions and accidents"}, "fines": {"title": "Traffic Fines", "description": "Contesting and handling traffic violations and penalties"}, "license": {"title": "License Suspension", "description": "Defending against and appealing driving license suspensions"}}, "inheritanceLaw": {"title": "Inheritance Law", "description": "Planning and resolving inheritance matters with sensitivity", "will": {"title": "Wills & Testaments", "description": "Creating legally sound wills to protect your legacy"}, "inheritance": {"title": "Inheritance Disputes", "description": "Resolving conflicts between heirs and beneficiaries"}, "estate": {"title": "Estate Planning", "description": "Comprehensive planning for asset distribution"}}}, "booking": {"title": "Book an Appointment", "subtitle": "Schedule a meeting with our team", "description": "Choose the type of meeting and duration that works best for you.", "bookingButton": "Request Booking", "successMessage": "Booking Request Sent!", "backToCalendar": "Book Another Meeting", "nameLabel": "Your Name", "emailLabel": "Email Address", "phoneLabel": "Phone Number", "messageLabel": "Additional Information", "bookingTypes": {"consultation": "Initial Consultation", "meeting": "Project Discussion", "support": "Support Session"}, "durations": {"short": "15 minutes", "medium": "30 minutes", "long": "60 minutes"}, "calendarButton": "Select Date", "submitButton": "Submit Booking Request", "backButton": "Back", "timeSlots": "View Available Times", "contactInfo": "Your Information", "selectDate": "Select Date", "selectTime": "Select Time", "yourInformation": "Your Information", "requestingBooking": "You are requesting a booking for", "pleaseSelectDate": "Please select a date to continue"}, "cta": {"badge": "Template Available", "title": "This template is available for full customization", "subtitle": "Transform your legal practice with a professional, modern website tailored to your needs", "features": {"seo": "SEO-optimized", "languages": "5-language support (EN, DE, TR, RU, AR)", "themes": "Dark/light theme", "integrations": "Notion & other integrations", "quality": "Premium Quality", "delivery": "Delivered in 7–10 business days", "support": "1-on-1 developer support"}, "buttons": {"templates": "See all templates", "consultation": "Book free consultation"}, "trust": {"secure": "100% Secure", "support": "24/7 Support", "guarantee": "Money-back guarantee"}}}