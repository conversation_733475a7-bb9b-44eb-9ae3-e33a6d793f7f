{"nav": {"home": "Главная", "about": "О нас", "solutions": "Решения", "services": "Услуги", "techStack": "Технологии", "portfolio": "Портфолио", "contact": "Контакты", "chatWithUs": "Чат с нами", "bookConsultation": "Записаться на консультацию", "apps": "Приложения", "pricing": "Цены", "prices": "Цены", "calculator": "Калькулятор цен", "clients": "Клиенты", "testimonials": "Отзывы"}, "hero": {"title": "Ваш надежный юридический партнер", "subtitle": "Экспертная юридическая помощь для частных лиц и бизнеса", "tagline": "Компетентность. Честность. Результат.", "description": "Положитесь на нашу команду опытных юристов для комплексных консультаций и эффективного представительства. Мы на вашей стороне по семейным, корпоративным и уголовным вопросам.", "typing": {"sequence1": "От идеи до App Store: Видение и код.", "sequence2": "Экспертная разработка приложений.", "sequence3": "Высокопроизводительные решения."}, "cta": {"primary": "Связаться с нами", "secondary": "Записаться на консультацию", "calculator": "Получить цену проекта сейчас"}, "metrics": {"development": {"label": "Быстрая разработка", "value": 40, "description": "40% быстрее разработка благодаря\nэффективным процессам и современным технологиям."}, "timeToMarket": {"label": "Быстрый выход на рынок", "value": 50, "description": "50% быстрее выход на рынок благодаря\nоптимизированной разработке MVP и развертыванию."}, "costSaving": {"label": "Экономия затрат", "value": 30, "description": "30% снижение затрат благодаря\nоптимизированной разработке и ресурсам."}}}, "about": {"title": "О нас", "subtitle": "Ваш партнер в сфере недвижимости", "description": "Благодаря многолетнему опыту на рынке недвижимости, наша команда профессионалов стремится помочь вам найти идеальную недвижимость или продать вашу текущую по наилучшей возможной цене.", "vision": "Видение", "visionDesc": "Стать самым надежным именем в сфере недвижимости, известным своей честностью, профессионализмом и стремлением превосходить ожидания клиентов в каждой сделке.", "mission": "Миссия", "missionDesc": "Мы стремимся предоставлять исключительные услуги в сфере недвижимости, сочетая глубокие знания рынка, персональное внимание и инновационные маркетинговые стратегии для достижения наилучших результатов для наших клиентов.", "founderTitle": "Наша команда", "founderDesc": "Наша команда состоит из опытных профессионалов с различным опытом, посвятивших себя созданию исключительных цифровых решений для наших клиентов.", "skills": "Экспертиза", "projects": "Проекты", "testimonials": "Отзывы", "experience": "Лет опыта", "clients": "Довольных клиентов", "transformBusiness": "Преобразование бизнеса с помощью технологий", "createSolutions": "Создание перспективных цифровых решений", "stayingAhead": "Лидерство в области технологий", "exceptionalUX": "Предоставление исключительного пользовательского опыта", "highPerformance": "Разработка высокопроизводительных приложений", "solvingChallenges": "Решение реальных бизнес-задач с помощью технологий", "flutterExpert": "Эксперт", "webDevAdvanced": "Продвинутый", "aiIntegration": "Интеграция", "successRate": "Процент успеха", "casesWon": "Выигранных дел", "publications": "Публикации", "meetTeam": "Познакомьтесь с нашей командой", "values": {"clientOrientation": {"title": "Ориентация на клиента", "description": "Каждое дело уникально – мы предлагаем индивидуальные, персонализированные консультации и активно защищаем ваши права."}, "expertise": {"title": "Профессионализм", "description": "Многолетний опыт и непрерывное обучение гарантируют высочайшее юридическое качество."}, "integrity": {"title": "Честность", "description": "Прозрачность, честность и конфиденциальность – основополагающие принципы нашей работы."}, "commitment": {"title": "Преданность делу", "description": "Мы страстно и целенаправленно боремся за интересы наших клиентов."}}, "team": {"lawyer1": {"name": "Д-р То<PERSON><PERSON><PERSON>ер", "role": "Основатель и специалист по семейному праву", "bio": "Д-р Томас Мюллер является основателем нашей юридической фирмы и экспертом в области семейного и наследственного права. С опытом более 20 лет и 96% успешных дел, он представляет клиентов в сложных бракоразводных процессах и делах об опеке. Как преподаватель Мюнхенского университета, он передает свои знания следующему поколению юристов.", "expertise": {"divorce": "Бракоразводное право", "custody": "Право опеки", "maintenance": "Алиментное право", "assets": "Раздел имущества"}}, "lawyer2": {"name": "Д-р Юлия Шмидт", "role": "Партнер и специалист по трудовому праву", "bio": "Д-р Юлия Шмидт специализируется в области трудового и договорного права с особым акцентом на защите от увольнения и правах сотрудников. Она представляет интересы как работников, так и средних компаний, и имеет 15-летний опыт в сложных трудовых спорах.", "expertise": {"dismissal": "Защита от увольнения", "contracts": "Трудовые договоры", "severance": "Выходные пособия", "workCouncil": "Право производственных советов"}}, "lawyer3": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "role": "Партнер и специалист по уголовному праву", "bio": "Мих<PERSON><PERSON>л Вебер – ваш защитник во всех уголовных делах. Благодаря своему аналитическому мышлению и обширному опыту в уголовной защите, он преданно отстаивает права своих клиентов и добился многочисленных оправдательных приговоров.", "expertise": {"economic": "Экономическое уголовное право", "defense": "Уголовная защита", "juvenile": "Ювенальное уголовное право"}}, "lawyer4": {"name": "Ека<PERSON><PERSON><PERSON><PERSON><PERSON> Бауэр", "role": "Специалист по транспортному праву", "bio": "Екатерина Бауэр – ваш эксперт в области транспортного права и урегулирования аварий. Она представляет клиентов в делах о штрафах, лишении водительских прав и требованиях о возмещении ущерба после дорожно-транспортных происшествий с впечатляющим показателем успеха.", "expertise": {"accident": "Урегулирование аварий", "fines": "Дела о штрафах", "license": "Право водительских удостоверений"}}, "lawyer5": {"name": "Александр Хоффманн", "role": "Специалист по договорному праву", "bio": "Александр Хоффманн специализируется на договорном и коммерческом праве. Он консультирует компании по составлению и проверке договоров, а также по вопросам обеспечения исполнения договорных требований с четким акцентом на экономических решениях.", "expertise": {"contractDesign": "Разработка договоров", "commercialLaw": "Коммерческое право", "termsConditions": "Право общих условий сделок"}}, "lawyer6": {"name": "Лиза Нейман", "role": "Специалист по наследственному праву", "bio": "Лиза Нейман оказывает поддержку по всем вопросам, связанным с завещаниями, наследованием и планированием преемственности. Благодаря своему чуткому подходу, она сопровождает клиентов в трудные времена и находит справедливые решения в наследственных спорах.", "expertise": {"willCreation": "Составление завещаний", "inheritance": "Наследственные споры", "compulsory": "Право обязательной доли"}}}, "expertise": {"familyLaw": {"title": "Семейное право", "description": "Компетентные консультации и представительство по всем вопросам семейного права, от развода и опеки до вопросов содержания."}, "employmentLaw": {"title": "Трудовое право", "description": "Всесторонняя поддержка в трудовых конфликтах, защита от увольнения и составление договоров для работников и работодателей."}, "criminalLaw": {"title": "Уголовное право", "description": "Преданная защита по уголовным делам на всех стадиях процесса с акцентом на оптимальную стратегию защиты."}, "trafficLaw": {"title": "Транспортное право", "description": "Обеспечение ваших прав при дорожно-транспортных происшествиях, штрафных процедурах и вопросах водительских прав."}}}, "advantages": {"title": "НАШИ РЕШЕНИЯ", "subtitle": "Как мы помогаем вам создавать успешные мобильные приложения", "speed": "Быстрая разработка", "speedDesc": "Мы быстро предоставляем решения без ущерба для качества", "stability": "Надежные приложения", "stabilityDesc": "Наши приложения созданы для стабильной и высокой производительности", "cost": "Экономическая эффективность", "costDesc": "Оптимизированный процесс разработки экономит ваше время и деньги", "timeToMarket": "Быстрый выход на рынок", "timeToMarketDesc": "Запускайте свой продукт быстро и опережайте конкурентов", "aiIntegration": "Интеграция ИИ", "aiIntegrationDesc": "Улучшите свой бизнес с помощью мощных возможностей искусственного интеллекта", "development": "Полноценная разработка", "developmentTime": "4-12 недель, зависит от сложности", "developmentDesc": "Полная разработка мобильного приложения от концепции до развертывания, с полной интеграцией бэкенда и расширенными функциями.", "mvp": "Разработка MVP", "mvpTime": "2-4 недели, зависит от сложности", "mvpDesc": "Быстрый запуск с минимально жизнеспособным продуктом, включающим основной функционал для проверки вашей концепции и привлечения первых пользователей или инвесторов.", "prototype": "Быстрое прототипирование", "prototypeTime": "1-2 недели, зависит от сложности", "prototypeDesc": "Быстрое тестирование концепций с интерактивными прототипами перед полноценной разработкой, экономя время и ресурсы.", "qa": "Контроль качества", "qaTime": "Постоянно, масштабируется в зависимости от сложности проекта", "qaDesc": "Комплексное тестирование на различных устройствах и платформах для обеспечения безупречной работы вашего приложения с автоматизированными и ручными протоколами тестирования.", "consulting": "Техническое консультирование", "consultingTime": "По необходимости, зависит от сложности проекта", "consultingDesc": "Экспертные рекомендации по технологическому стеку, архитектурным решениям и стратегиям реализации для оптимизации вашего мобильного приложения.", "uiux": "UI/UX-дизайн", "uiuxTime": "2-3 нед<PERSON><PERSON><PERSON>, зависит от сложности", "uiuxDesc": "Ориентированный на пользователя дизайн, который сочетает эстетику с функциональностью, создавая интуитивно понятный и привлекательный мобильный опыт.", "maintenance": "Поддержка и сопровождение", "maintenanceTime": "Постоянно, масштабируется в зависимости от сложности проекта", "maintenanceDesc": "Долгосрочная поддержка с регулярными обновлениями, оптимизацией производительности и улучшением функций для поддержания конкурентоспособности вашего приложения.", "analytics": "Интеграция аналитики", "analyticsTime": "1-2 недели, зависит от сложности", "analyticsDesc": "Внедрение отслеживания данных для получения практических сведений о поведении пользователей, что позволяет принимать обоснованные решения для вашего приложения.", "training": "Обучение команды", "trainingTime": "1-2 недели, зависит от сложности", "trainingDesc": "Комплексное обучение вашей команды по поддержке и расширению вашего приложения после передачи.", "developmentEfficiency": "Эффективность разработки", "timeToMarketReduction": "Сокращение времени выхода на рынок", "conceptValidation": "Проверка концепции", "bugFreeRate": "Показатель отсутствия ошибок", "technicalImprovement": "Техническое улучшение", "userSatisfaction": "Удовлетворенность пользователей", "appUptime": "Бесперебойная работа приложения", "dataAccuracy": "Точность данных", "knowledgeRetention": "Сохранение знаний", "developmentInfo": {"title": "Сроки разработки", "simpleApp": {"title": "Простое приложение", "examples": "Примеры: списки дел, калькуляторы, простые информационные приложения без бэкенда.", "features": ["Несколько экранов (3-5)", "Отсутствие или минимальная интеграция с бэкендом", "Стандартные компоненты интерфейса", "Отсутствие сложных анимаций или функций"], "timeline": {"total": "Время разработки: 4-8 недель", "frontend": "Фронтенд: 2-4 недели", "backend": "Бэкенд (если требуется): 1-2 недели", "testing": "Тестирование и развертывание: 1-2 недели"}}, "mediumApp": {"title": "Среднее приложение", "examples": "Примеры: приложения электронной коммерции, социальные сети с базовыми функциями, приложения с регистрацией пользователей и интеграцией с базой данных.", "features": ["6-15 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Интеграция с бэкендом (например, REST или GraphQL API)", "Регистрация и аутентификация пользователей", "База данных для пользователей и данных приложения", "Некоторые анимации и интерактивные элементы", "Push-уведомления"], "timeline": {"total": "Время разработки: 8-16 недель", "frontend": "Фронтенд: 4-6 недель", "backend": "Бэкенд: 3-5 недель", "testing": "Тестирование и развертывание: 2-3 недели"}}, "complexApp": {"title": "Сложное приложение", "examples": "Примеры: приложения типа Uber, Instagram или банковские приложения с расширенными функциями.", "features": ["15+ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Высокоинтерактивный пользовательский интерфейс", "Функции реального времени (например, отслеживание в реальном времени, чат)", "Интеграция сторонних API (например, платежные шлюзы, API карт)", "Масштабируемый бэкенд с облачной интеграцией", "Функции безопасности (например, шифрование, двухфакторная аутентификация)", "Офлайн-функциональность"], "timeline": {"total": "Время разработки: 16-32 недели или дольше", "frontend": "Фронтенд: 6-10 недель", "backend": "Бэкенд: 6-12 недель", "testing": "Тестирование и развертывание: 4-6 недель"}}, "factors": {"title": "Факторы, влияющие на время разработки", "teamSize": "Размер команды: большая команда (например, отдельные разработчики для фронтенда, бэкенда и QA) может ускорить разработку. Один разработчик требует больше времени.", "technology": "Технологии: нативная разработка (например, Swift для iOS, Kotlin для Android) часто занимает больше времени, чем кроссплатформенные подходы, такие как Flutter или React Native. Кроссплатформенные фреймворки могут сократить время разработки на 30-40%.", "requirements": "Требования и изменения: частые изменения или неясные требования могут продлить время разработки.", "testing": "Тестирование и отладка: сложные приложения требуют больше времени для тестирования, особенно на нескольких платформах (iOS и Android).", "design": "Дизайн: простые дизайны требуют меньше времени, в то время как пользовательские, анимированные дизайны увеличивают время разработки."}, "summary": "Итого: Простое приложение: 4-8 недель. Среднее приложение: 8-16 недель. Сложное приложение: 16-32 недели или дольше.", "aiComparison": "С нашей разработкой на основе ИИ и Flutter мы можем сократить эти сроки на 40-60%, сохраняя при этом высокое качество и производительность."}}, "serviceSection": {"title": "Наши услуги", "subtitle": "Индивидуальные цифровые решения для вашего бизнеса", "description": "Мы предлагаем широкий спектр цифровых услуг, чтобы помочь бизнесу процветать в современной конкурентной среде. Наша экспертиза охватывает различные области для предоставления инновационных и эффективных решений.", "viewAll": "Обсудить ваш проект", "mobileApps": {"title": "Разработка мобильных приложений", "description": "Кроссплатформенные мобильные приложения с нативной производительностью на Flutter, обеспечивающие безупречный опыт на устройствах iOS и Android.", "benefits": ["На 40% быстрее выход на рынок по сравнению с нативной разработкой", "Единая кодовая база для платформ iOS и Android", "Нативная производительность и красивый пользовательский интерфейс"]}, "webDev": {"title": "Веб-разработка", "description": "Создайте веб-сайт, который по-настоящему представляет ваш бренд и привлекает вашу аудиторию. Мы делаем это просто, красиво и эффективно.", "benefits": ["SEO-оптимизированная производительность с серверным рендерингом", "Адаптивный дизайн, работающий на всех устройствах", "Масштабируемая архитектура для будущего роста"]}, "uiuxDesign": {"title": "UI/UX-дизайн", "description": "Интуитивный, ориентированный на пользователя дизайн, который восхищает клиентов и повышает вовлеченность благодаря продуманному пользовательскому опыту.", "benefits": ["Подход к дизайну, ориентированный на пользователя", "Интерфейсы, оптимизированные для конверсии", "Единый опыт бренда на всех платформах"]}, "consulting": {"title": "Техническое консультирование", "description": "Экспертные рекомендации по технологической стратегии и реализации, помогающие принимать обоснованные решения для ваших цифровых проектов.", "benefits": ["Рекомендации по технологическому стеку", "Планирование и анализ архитектуры", "Оптимизация производительности и безопасности"]}, "aiSolutions": {"title": "Интеграция ИИ", "description": "Внедрение возможностей искусственного интеллекта для улучшения ваших бизнес-приложений с помощью интеллектуальных функций и автоматизации.", "benefits": ["Персонализированный пользовательский опыт", "Автоматизированные рабочие процессы", "Аналитика данных и прогнозирование"]}, "prototype": {"title": "Быстрое прототипирование", "description": "Быстрое тестирование концепций с интерактивными прототипами перед полноценной разработкой, экономя время и ресурсы.", "benefits": ["Проверка идей с минимальными инвестициями", "Сбор отзывов пользователей на ранних этапах", "Доработка концепций перед полной разработкой"]}, "mvp": {"title": "Разработка MVP", "description": "Быстрый запуск минимально жизнеспособного продукта с основным функционалом для проверки вашей концепции и привлечения первых пользователей.", "benefits": ["Более быстрый выход на рынок с основными функциями", "Экономичный подход к проверке бизнес-идей", "Итеративная разработка на основе реальных отзывов пользователей"]}, "fullstack": {"title": "Полноценная разработка", "description": "Полная разработка приложений от концепции до развертывания с интегрированными решениями для фронтенда и бэкенда.", "benefits": ["Комплексная экспертиза разработки", "Безупречная интеграция между всеми компонентами системы", "Всестороннее тестирование и контроль качества"]}}, "services": {"title": "Наш технологический стек", "subtitle": "Комплексные цифровые решения", "description": "Мы используем передовые технологии в различных областях для создания надежных и масштабируемых решений для ваших бизнес-потребностей.", "frontend": "Frontend-разработка", "frontendDesc": "Создание отзывчивых и интерактивных пользовательских интерфейсов с использованием современных веб-технологий", "backend": "Backend-разработка", "backendDesc": "Создание надежных серверных решений и API для масштабируемых приложений", "mobile": "Мобильная разработка", "mobileDesc": "Разработка кроссплатформенных мобильных приложений с нативной производительностью", "ai": "ИИ и машинное обучение", "aiDesc": "Интеграция интеллектуальных функций и автоматизации в приложения", "mobileApps": "Разработка мобильных приложений", "mobileAppsDesc": "Кроссплатформенные приложения с Flutter для iOS и Android", "webDev": "Веб-разработка", "webDevDesc": "Современные, адаптивные веб-сайты и веб-приложения", "uiuxDesign": "UI/UX-дизайн", "uiuxDesignDesc": "Интуитивно понятный, ориентированный на пользователя дизайн, который восхищает клиентов", "consulting": "Техническое консультирование", "consultingDesc": "Экспертные советы по технологической стратегии и реализации", "aiSolutions": "Интеграция ИИ", "aiSolutionsDesc": "Внедрение возможностей ИИ для улучшения вашего бизнеса", "viewAll": "Просмотреть все услуги"}, "prices": {"title": "Наш калькулятор цен", "subtitle": "Рассчитайте здесь ваше желание", "description": "Выберите из наших предопределенных пакетов или настройте их в соответствии с требованиями вашего проекта. Объедините услуги для получения скидки 15%.", "metaTitle": "Интерактивный калькулятор цен | Получите индивидуальное предложение", "metaDescription": "Используйте наш интерактивный калькулятор цен, чтобы получить индивидуальное предложение, адаптированное к потребностям вашего проекта. Прозрачные цены без скрытых платежей.", "caseStudyTitle": "Пример: на 40% быстрее выход на рынок", "caseStudyDescription": "Наш финтех-клиент из Дубая запустил свой MVP на 40% быстрее среднего по отрасли, что позволило ему получить дополнительное финансирование и ускорить рост.", "promotionTitle": "Объедините услуги и сэкономьте 15%", "promotionDescription": "Объедините любые две или более услуги и получите скидку 15% на общую стоимость проекта.", "calculatorTitle": "Нужно индивидуальное предложение?", "calculatorDescription": "Используйте наш интерактивный калькулятор цен, чтобы получить детальную оценку, адаптированную к конкретным требованиям вашего проекта.", "calculatorButton": "Попробуйте наш калькулятор цен", "discussButton": "Обсудить ваш проект", "contactButton": "Связаться с нами", "packages": {"mvp": {"title": "Разработка MVP", "timeframe": "3-4 недели", "description": "Быстро запустите свою идею с минимально жизнеспособным продуктом", "features": ["Реализация основного функционала", "Базовый дизайн интерфейса", "Аутентификация пользователей", "Решение для хранения данных", "Развертывание на одной платформе", "Соответствие требованиям Дубая"]}, "prototype": {"title": "Быстрый прототип", "timeframe": "1-2 недели", "description": "Протестируйте свою концепцию с функциональным прототипом", "features": ["Интерактивные макеты интерфейса", "Базовая функциональность", "Реализация пользовательского потока", "Презентация для заинтересованных сторон", "Разработка MVP на основе Flutter"]}, "architecture": {"title": "Архитектура проекта", "timeframe": "1-2 недели", "description": "Прочная основа для успеха вашего проекта", "features": ["Технические спецификации", "Диз<PERSON><PERSON>н системной архитектуры", "Схема базы данных", "Документация API", "Дорожная карта разработки"]}, "consulting": {"title": "Техническое консультирование", "timeframe": "Постоянно", "description": "Экспертное руководство для ваших технических решений", "features": ["Рекомендации по технологическому стеку", "Обзоры кода", "Оптимизация производительности", "Оценка безопасности", "Планирование масштабируемости", "Интеграция платежного шлюза ОАЭ"]}}}, "packages": {"title": "Наши пакеты", "subtitle": "Индивидуальные решения для ваших потребностей", "mvp": {"title": "Разработка MVP", "timeframe": "3-4 недели", "description": "Быстро запустите свою идею с минимально жизнеспособным продуктом", "features": ["Реализация основного функционала", "Базовый UI-дизайн", "Аутентификация пользователей", "Решение для хранения данных", "Развертывание на одной платформе"]}, "prototype": {"title": "Пакет прототипирования", "timeframe": "1-2 неделя", "description": "Протестируйте свою концепцию с функциональным прототипом", "features": ["Интерактивные UI-макеты", "Базовая функциональность", "Реализация пользовательского потока", "Презентация для заинтересованных сторон"]}, "architecture": {"title": "Архитектура проекта", "timeframe": "1-2 недели", "description": "Прочная основа для успеха вашего проекта", "features": ["Технические спецификации", "Диз<PERSON><PERSON>н системной архитектуры", "Схема базы данных", "Документация API", "Дорожная карта разработки"]}, "consulting": {"title": "Техническое консультирование", "timeframe": "Постоянно", "description": "Экспертное руководство для ваших технических решений", "features": ["Рекомендации по технологическому стеку", "Обзоры кода", "Оптимизация производительности", "Оценка безопасности", "Планирование масштабируемости"]}}, "solutionsPortfolio": {"title": "Наши решения", "subtitle": "Демонстрация передовых цифровых решений", "clickInstruction": "Нажмите на карточку для подробностей и полноэкранного просмотра", "imageCounter": "Изображение {current} из {total}", "keyFeatures": "Ключевые функции", "problemsSolved": "Решаемые проблемы", "viewDetails": "Подробнее", "screenshot": "Скриншот", "screenshots": "Скриншоты", "categories": {"all": "Все Категории", "aiAssistant": "ИИ-Aссистент", "foodDelivery": "Доставка Еды", "hospitality": "Гостиничный Бизнес", "medical": "Медицина", "lifestyle": "Лайфстайл Приложения", "automotive": "Автомобильная Отрасль"}, "items": {"spotzAiAssistant": {"title": "Spotz ИИ-ассистент", "description": "ИИ-ассистент для поиска ближайших мест, предоставления рекомендаций, навигации и экстренных служб.", "imageAlt": "Скриншот интерфейса мобильного приложения Spotz ИИ-ассистент {index}"}, "foodDelivery": {"title": "Доставка еды", "description": "Полное решение для поиска и доставки еды с интуитивным процессом заказа.", "imageAlt": "Скриншот приложения доставки еды {index}"}, "hostIQ": {"title": "HostIQ - СуперПриложение для гостиничного бизнеса", "description": "Комплексное решение для отелей по управлению операциями, опытом гостей и услугами.", "imageAlt": "Скриншот платформы управления гостиничным бизнесом HostIQ {index}"}, "businessManagement": {"title": "Управление локальным бизнесом", "description": "Маркетинговые инструменты, размещение событий, обмен историями и управление бронированием для локального бизнеса.", "imageAlt": "Скриншот приложения для управления бизнесом {index}"}, "lumeusApp": {"title": "Приложе<PERSON><PERSON><PERSON> Lumeus", "description": "Платформа для социальных сетей и связей с расширенными функциями.", "imageAlt": "Скриншот приложения для социальных сетей Lumeus {index}"}, "nearby": {"title": "Поблизости", "description": "Приложение для поиска на основе местоположения, чтобы найти всё в вашем районе.", "imageAlt": "Скриншот интерфейса приложения Поблизости {index}"}, "toggCarControl": {"title": "Управление автомобилем Togg", "description": "Интеллектуальная система управления и контроля автомобилей Togg.", "imageAlt": "Скриншот приложения управления автомобилем Togg {index}"}, "socialMediaPlatform": {"title": "Платформа социальных сетей", "description": "Современная платформа социальных сетей с привлекательным пользовательским опытом.", "imageAlt": "Скриншот интерфейса платформы социальных сетей {index}"}, "default": {"imageAlt": "Скриншот элемента портфолио {index}"}}, "solutions": {"aiAssistant": {"title": "Решения с ИИ-ассистентами", "description": "Интеллектуальные виртуальные помощники, улучшающие пользовательский опыт и автоматизирующие задачи.", "features": ["Обработка естественного языка", "Контекстное понимание", "Распознавание голоса", "Поддержка нескольких платформ", "Возможности интеграции"], "problemsSolved": ["Доступ к информации", "Поддержка принятия решений", "Автоматизация обслуживания клиентов", "Управление задачами"]}, "foodDelivery": {"title": "Платформы доставки еды", "description": "Комплексные системы для ресторанов и служб доставки.", "features": ["Управление заказами", "Отслеживание в реальном времени", "Обработка платежей", "Панель управления рестораном", "Оптимизация доставки"], "problemsSolved": ["Поиск ресторанов", "Логистика доставки", "Выполнение заказов", "Удержание клиентов"]}, "hospitality": {"title": "Решения для управления гостиничным бизнесом", "description": "Цифровые системы, улучшающие опыт гостей и оптимизирующие операции.", "features": ["Управление бронированием", "Услуги для гостей", "Координация персонала", "Контроль запасов", "Аналитическая панель"], "problemsSolved": ["Оптимизация опыта гостей", "Операционная эффективность", "Управление ресурсами", "Предоставление услуг"]}, "business": {"title": "Системы управления бизнесом", "description": "Комплексные платформы для управления операциями и ростом.", "features": ["Интеграция с CRM", "Управление запасами", "Отслеживание финансов", "Управление персоналом", "Инструменты отчетности"], "problemsSolved": ["Оптимизация процессов", "Управление данными", "Распределение ресурсов", "Бизнес-аналитика"]}, "social": {"title": "Платформы социальных сетей", "description": "Привлекательные решения для социальных сетей и сообществ.", "features": ["Профили пользователей", "Об<PERSON>ен контентом", "Система сообщений", "Система уведомлений", "Аналитические инструменты"], "problemsSolved": ["Создание цифровых сообществ", "Вовлечение пользователей", "Обнаружение контента", "Социальное взаимодействие"]}, "automotive": {"title": "Решения автомобильных технологий", "description": "Цифровые интерфейсы и системы управления для транспортных средств.", "features": ["Управление автомобилем", "Навигационные системы", "Диагностические инструменты", "Интеграция развлечений", "Подключение IoT"], "problemsSolved": ["Контроль транспортного средства", "Проблемы навигации", "Управление техническим обслуживанием", "Опыт водителя"]}}}, "portfolio": {"title": "Наша экспертиза", "subtitle": "Отрасли и решения проблем", "all": "Все отрасли", "screenshot": "Снимок экрана", "screenshots": "Снимки экрана", "problemsWeSolve": "Проблемы, которые мы решаем", "noSectorsFound": "Не найдено отраслей для выбранного фильтра.", "categories": {"aiAssistant": "ИИ-ассистент", "foodDelivery": "Доставка еды", "hospitality": "Гостиничный бизнес", "business": "Бизнес", "social": "Социальные сети", "automotive": "Автомобильная индустрия"}, "sectors": {"assistant": "Приложения-ассистенты", "food": "Заказ еды и доставка", "hospitality": "Гостиничный бизнес", "lifestyle": "Лайфстайл-приложения", "social": "Социальные сети", "automotive": "Автомобильная индустрия", "medical": "Медицина и здравоохранение", "business": "Бизнес-решения"}, "sectorDescriptions": {"assistant": "ИИ-помощники, которые повышают продуктивность и предоставляют персональные рекомендации", "food": "Платформы для заказа и доставки еды с отслеживанием в реальном времени", "hospitality": "Цифровые решения для отелей и гостиничного бизнеса для улучшения впечатлений гостей", "lifestyle": "Приложения, которые улучшают повседневную жизнь, благополучие и личностное развитие", "social": "Платформы, соединяющие людей и сообщества через общие интересы", "automotive": "Умные решения для управления автомобилем, навигации и помощи водителю", "medical": "Цифровые решения в области здравоохранения, улучшающие уход за пациентами и медицинские операции", "business": "Корпоративные приложения, оптимизирующие операции и повышающие производительность"}, "problems": {"assistant": {"1": "Информационная перегрузка", "2": "Управление задачами", "3": "Поддержка принятия решений"}, "food": {"1": "Управление заказами", "2": "Логистика доставки", "3": "Поиск ресторанов"}, "hospitality": {"1": "Управление гостями", "2": "Оптимизация сервиса", "3": "Системы бронирования"}, "lifestyle": {"1": "Отслеживание здоровья", "2": "Формирование привычек", "3": "Личная организация"}, "social": {"1": "Вовлеченность пользователей", "2": "Поиск контента", "3": "Построение сообществ"}, "automotive": {"1": "Мониторинг транспортных средств", "2": "Оптимизация навигации", "3": "Опыт водителя"}, "medical": {"1": "Управление пациентами", "2": "Монитор<PERSON>нг здоровья", "3": "Системы медицинских записей"}, "business": {"1": "Оптимизация рабочих процессов", "2": "Управление данными", "3": "Командное сотрудничество"}}, "viewDetails": "Посмотреть детали", "viewAllProjects": "Посмотреть все проекты"}, "clients": {"title": "Ваш новый сайт", "subtitle": "Единственный предел - ваше воображение", "visitWebsite": "Посетить сайт"}, "testimonials": {"title": "Отзывы клиентов", "subtitle": "Что говорят наши клиенты", "description": "Прочит<PERSON><PERSON><PERSON>е, что говорят наши клиенты о своем опыте работы с нами и о результатах, которые мы достигли.", "readMore": "Читать далее", "readLess": "Свернуть", "formTitle": "Поделитесь своим опытом", "name": "Имя", "email": "Эл. почта", "yourName": "Ваше имя", "yourEmail": "Ваша эл. почта", "caseType": "Область права", "rating": "<PERSON>ей<PERSON>инг", "stars": "Звезд", "yourExperience": "Ваш опыт", "experienceDescription": "Опишите ваш опыт работы с нашей юридической фирмой...", "submitReview": "Отправить отзыв", "selectOption": "Пожалуйста, выберите", "familyLaw": "Семейное право", "employmentLaw": "Трудовое право", "criminalLaw": "Уголовное право", "trafficLaw": "Транспортное право", "contractLaw": "Договорное право"}, "contact": {"title": "Связаться с нами", "subtitle": "Свяжитесь с нами", "description": "Свяжитесь с нами по любым вопросам, обсуждению проектов или чтобы записаться на консультацию. Мы поможем воплотить ваши идеи в жизнь.", "name": "Имя", "email": "Email", "phone": "Телефон", "message": "Сообщение", "send": "Отправить сообщение", "yourName": "Ваше имя", "yourEmail": "Ваш email", "subject": "Тема", "howCanIHelp": "Чем я могу помочь?", "yourMessageHere": "Ваше сообщение здесь", "getInTouch": "Связаться с нами", "sendMessage": "Отправить сообщение", "schedule": "Запланировать звонок", "freeConsultation": "Забронировать бесплатную 15-минутную консультацию", "location": "Местоположение", "submitButton": "Отправить сообщение", "sending": "Отправка...", "messageSent": "Сообщение отправлено!", "errorTryAgain": "Ошибка, попробуйте снова", "orSchedule": "Или запланируйте встречу напрямую, используя ссылку календаря", "whatsapp": "WhatsApp", "linkedin": "LinkedIn", "github": "GitHub", "booking": "Записаться на прием", "directions": "Проложить маршрут"}, "footer": {"copyright": "© 2025 Company Name. Все права защищены.", "description": "Мы разрабатываем передовые мобильные и веб-приложения, которые трансформируют бизнес с помощью инновационных технологий.", "quickLinks": "Быстрые ссылки", "footerContact": "Контакты", "legal": "Правовая информация", "newsletter": "Рассылка", "newsletterDesc": "Подпишитесь на нашу рассылку, чтобы получать обновления и информацию.", "emailPlaceholder": "Введите ваш email", "subscribe": "Подписаться", "builtWith": "Создано с помощью", "and": "и", "downloadCV": "Моё резюме", "englishCV": "Английский", "germanCV": "Немецкий"}, "cookies": {"title": "Согласие на использование cookie", "description": "Мы используем файлы cookie для улучшения вашего опыта просмотра, показа персонализированной рекламы или контента и анализа нашего трафика. Нажимая «Принять все», вы соглашаетесь с использованием нами файлов cookie.", "acceptAll": "Принять все", "decline": "Отклонить", "customize": "Настроить", "necessary": "Необходимые cookies", "necessaryDesc": "Эти файлы cookie необходимы для правильного функционирования веб-сайта и не могут быть отключены.", "analytics": "Аналитические cookies", "analyticsDesc": "Эти файлы cookie помогают нам понять, как посетители взаимодействуют с нашим веб-сайтом, и помогают улучшить наши услуги.", "marketing": "Маркетинговые cookies", "marketingDesc": "Эти файлы cookie используются для отслеживания посетителей на веб-сайтах для отображения релевантной рекламы.", "functional": "Функциональные cookies", "functionalDesc": "Эти файлы cookie обеспечивают расширенную функциональность и персонализацию на нашем веб-сайте.", "save": "Сохранить настройки", "settings": "Настройки cookie", "close": "Закрыть", "cookiePolicy": "Политика использования cookie", "privacyPolicy": "Политика конфиденциальности"}, "heroParallax": {"title": "Лучшая студия разработки", "subtitle": "Мы создаем прекрасные продукты с использованием новейших технологий и фреймворков. Мы - команда увлеченных разработчиков и дизайнеров, которые любят создавать удивительные продукты.", "products": {"mobileApp": "Разработка мобильных приложений", "webDev": "Веб-разработка", "uiux": "UI/UX дизайн", "ecommerce": "Решения для электронной коммерции", "ai": "Интеграция ИИ", "cloud": "Облачные решения", "devops": "DevOps", "dataAnalytics": "Анализ данных", "blockchain": "Разработка блокчейн", "arvr": "AR/VR решения", "customSoftware": "Индивидуальное ПО", "mobileGame": "Разработка мобильных игр", "iot": "IoT решения", "api": "Разработка API", "cybersecurity": "Кибербезопасность"}}, "featuresSection": {"features": [{"title": "Создано для разработчиков", "description": "Создано для инженеров, разра<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, мечтателе<PERSON>, мыслителей и деятелей.", "icon": "IconTerminal2"}, {"title": "Простота использования", "description": "Это так же просто, как использовать Apple, и так же дорого, как купить его.", "icon": "IconEaseInOut"}, {"title": "Ценообразование, как ни у кого другого", "description": "Наши цены лучшие на рынке. Без ограничений, без блокировки, кредитная карта не требуется.", "icon": "Icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "Гарантия 100% времени безотказной работы", "description": "Нас просто невозможно вывести из строя.", "icon": "IconCloud"}, {"title": "Мультитенантная архитектура", "description": "Вы можете просто делиться паролями вместо покупки новых мест.", "icon": "IconRouteAltLeft"}, {"title": "Круглосуточная поддержка клиентов", "description": "Мы доступны 100% времени. По крайней мере, наши ИИ-агенты.", "icon": "IconHelp"}, {"title": "Гарантия возврата денег", "description": "Если вам не понравится EveryAI, мы убедим вас полюбить нас.", "icon": "IconAdjustmentsBolt"}, {"title": "И все остальное", "description": "У меня просто закончились идеи для текста. Примите мои искренние извинения.", "icon": "IconHeart"}]}, "legalServices": {"familyLaw": {"title": "Семейное право", "description": "Профессиональная юридическая поддержка для семей в сложных эмоциональных ситуациях", "divorce": {"title": "Развод", "description": "Чуткое сопровождение процесса расторжения брака"}, "maintenance": {"title": "Алименты", "description": "Обеспечение справедливой финансовой поддержки для членов семьи"}, "custody": {"title": "Опека и посещение", "description": "Защита интересов детей в семейных спорах"}}, "employmentLaw": {"title": "Трудовое право", "description": "Защита прав работников и поддержка работодателей в соблюдении законодательства", "protection": {"title": "Защита работников", "description": "Защита от незаконного увольнения и преследования на рабочем месте"}, "contract": {"title": "Трудовые договоры", "description": "Составление и проверка справедливых трудовых соглашений"}, "warning": {"title": "Дисциплинарные взыскания", "description": "Правильное оформление дисциплинарных процедур на рабочем месте"}}, "contractLaw": {"title": "Договорное право", "description": "Профессиональное составление договоров и разрешение споров для всех типов контрактов", "purchase": {"title": "Договоры купли-продажи", "description": "Безопасные и справедливые договоры для покупки недвижимости и активов"}, "service": {"title": "Договоры на оказание услуг", "description": "Четкие и защищающие соглашения об услугах для бизнеса"}, "work": {"title": "Договоры подряда", "description": "Комплексные условия найма и трудовые условия"}}, "criminalLaw": {"title": "Уголовное право", "description": "Сильная защита и представительство в уголовных процессах", "defense": {"title": "Уголовная защита", "description": "Энергичная защита против уголовных обвинений"}, "victimProtection": {"title": "Защита потерпевших", "description": "Поддержка и представительство жертв преступлений"}, "juvenile": {"title": "Ювенальное право", "description": "Специализированный подход к несовершеннолетним правонарушителям"}}, "civilLaw": {"title": "Гражданское право", "description": "Разрешение споров между частными лицами и организациями", "damages": {"title": "Иски о возмещении ущерба", "description": "Взыскание ущерба за телесные повреждения или имущество"}, "disputes": {"title": "Соседские споры", "description": "Медиация и разрешение имущественных конфликтов"}, "general": {"title": "Общие гражданские дела", "description": "Представительство в различных гражданских вопросах"}}, "trafficLaw": {"title": "Транспортное право", "description": "Профессиональная помощь по всем вопросам, связанным с дорожным движением", "accident": {"title": "Дорожно-транспортные происшествия", "description": "Юридическая поддержка после столкновений и аварий"}, "fines": {"title": "Штрафы за нарушение ПДД", "description": "Оспаривание и обработка нарушений правил дорожного движения и штрафов"}, "license": {"title": "Лишение водительских прав", "description": "Защита от лишения и обжалование приостановки водительских прав"}}, "inheritanceLaw": {"title": "Наследственное право", "description": "Планирование и разрешение наследственных вопросов с деликатностью", "will": {"title": "Завещания", "description": "Создание юридически обоснованных завещаний для защиты вашего наследия"}, "inheritance": {"title": "Наследственные споры", "description": "Разрешение конфликтов между наследниками и бенефициарами"}, "estate": {"title": "Планирование наследства", "description": "Комплексное планирование распределения активов"}}}, "booking": {"title": "Записаться на прием", "subtitle": "Запланируйте встречу с нашей командой", "description": "Выберите подходящий тип и длительность встречи.", "bookingButton": "Запросить запись", "successMessage": "Запрос на запись отправлен!", "backToCalendar": "Записаться на другую встречу", "nameLabel": "Ваше имя", "emailLabel": "Электронная почта", "phoneLabel": "Телефон", "messageLabel": "Дополнительная информация", "bookingTypes": {"consultation": "Первичная консультация", "meeting": "Обсуждение проекта", "support": "Сессия поддержки"}, "durations": {"short": "15 минут", "medium": "30 минут", "long": "60 минут"}, "calendarButton": "Выбрать дату", "submitButton": "Отправить запрос", "backButton": "Назад", "timeSlots": "Показать доступное время", "contactInfo": "Ваша информация", "selectDate": "Выберите дату", "selectTime": "Выберите время", "yourInformation": "Ваша информация", "requestingBooking": "Вы запрашиваете запись на", "pleaseSelectDate": "Пожалуйста, выберите дату для продолжения"}, "cta": {"badge": "Шаблон доступен", "title": "Этот шаблон доступен для полной кастомизации", "subtitle": "Преобразите свою юридическую практику с профессиональным, современным веб-сайтом, адаптированным под ваши потребности", "features": {"seo": "SEO-оптимизированный", "languages": "Поддержка 5 языков (EN, DE, TR, RU, AR)", "themes": "Темная/светлая тема", "integrations": "Notion и другие интеграции", "quality": "Премиум качество", "delivery": "Доставка за 7-10 рабочих дней", "support": "Индивидуальная поддержка разработчика"}, "buttons": {"templates": "Посмотреть все шаблоны", "consultation": "Забронировать бесплатную консультацию"}, "trust": {"secure": "100% безопасно", "support": "Поддержка 24/7", "guarantee": "Гарантия возврата денег"}}}