{"nav": {"home": "Startseite", "about": "Über uns", "solutions": "Lösungen", "services": "Dienstleistungen", "techStack": "Tech Stack", "portfolio": "Portfolio", "contact": "Kontakt", "chatWithUs": "Chat mit uns", "bookConsultation": "Beratung buchen", "apps": "Apps", "pricing": "<PERSON><PERSON>", "prices": "<PERSON><PERSON>", "calculator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clients": "<PERSON><PERSON>", "testimonials": "Referenzen"}, "hero": {"title": "Ihr vertrauenswürdiger Partner für Recht", "subtitle": "Kompetente Rechtsberatung für Privatpersonen und Unternehmen", "tagline": "Kompetenz. Integrität. Erfolg.", "description": "Verlassen Sie sich auf unser erfahrenes Anwaltsteam für umfassende Beratung und effektive Vertretung. Ob Familien-, Wirtschafts- oder Strafrecht – wir stehen an Ihrer Seite.", "typing": {"sequence1": "Luxuswohnungen in Bestlage.", "sequence2": "Familienhäuser mit Raum zum Wachsen.", "sequence3": "Renditestarke Anlageobjekte."}, "cta": {"primary": "Kontakt aufnehmen", "secondary": "<PERSON><PERSON><PERSON> buchen", "calculator": "Preisschätzung erhalten"}, "metrics": {"development": {"label": "Schnellere Entwicklung", "value": 40, "description": "40% schnellere Entwicklung durch\neffiziente Prozesse & moderne Technologie."}, "timeToMarket": {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": 50, "description": "50% schnellerer Markteintritt durch\noptimierte Workflows & Bereitstellung."}, "costSaving": {"label": "Kosteneinsparung", "value": 30, "description": "30% Kostenreduzierung durch\noptimierte Entwicklung & Ressourcen."}}}, "about": {"title": "Über Uns", "subtitle": "<PERSON>hr Immobilienpartner", "description": "Mit jahrelanger Erfahrung im Immobilienmarkt ist unser Team engagierter Fachleute bestrebt, Ihnen beim Finden der perfekten Immobilie oder beim Verkauf Ihrer aktuellen zum bestmöglichen Preis zu helfen.", "vision": "Vision", "visionDesc": "Wir wollen der vertrauenswürdigste Name im Immobilienbereich sein, bekannt für unsere Integrität, Sachkenntnis und das Engagement, die Erwartungen der Kunden bei jeder Transaktion zu übertreffen.", "mission": "Mission", "missionDesc": "Wir bemühen uns, außergewöhnliche Immobiliendienstleistungen zu erbringen, indem wir tiefes Marktwissen, persönliche Aufmerksamkeit und innovative Marketingstrategien kombinieren, um die bestmöglichen Ergebnisse für unsere Kunden zu erzielen.", "founderTitle": "Unser Team", "founderDesc": "Unser Team besteht aus erfahrenen Fachleuten verschiedener Hintergründe, die sich der Schaffung außergewöhnlicher digitaler Erlebnisse für unsere Kunden widmen.", "skills": "Expertise", "projects": "Projekte", "testimonials": "Testimonials", "experience": "<PERSON><PERSON><PERSON>", "clients": "Zufriedene Kunden", "transformBusiness": "Unternehmen durch Technologie transformieren", "createSolutions": "Zukunftssichere digitale Lösungen schaffen", "stayingAhead": "An der Spitze der Technologie bleiben", "exceptionalUX": "Außergewöhnliche Benutzererlebnisse liefern", "highPerformance": "Hochleistungs-Websites erstellen", "solvingChallenges": "Reale Geschäftsherausforderungen mit Technologie lösen", "flutterExpert": "Experte", "webDevAdvanced": "Fortgeschritten", "aiIntegration": "Integration", "successRate": "Erfolgsquote", "casesWon": "Gewonnene Fälle", "publications": "Publikationen", "meetTeam": "Team kennenlernen", "values": {"clientOrientation": {"title": "Mandantenorientierung", "description": "<PERSON>er Fall ist einzigartig – wir bieten individuelle, persönliche Beratung und setzen uns engagiert für Ihre Rechte ein."}, "expertise": {"title": "Fachkompetenz", "description": "Langjährige Erfahrung und kontinuierliche Weiterbildung garantieren höchste juristische Qualität."}, "integrity": {"title": "Integrität", "description": "Transparenz, Ehrlichkeit und Diskretion sind die Grundpfeiler unserer Arbeit."}, "commitment": {"title": "Engagement", "description": "Wir kämpfen leidenschaftlich und zielstrebig für die Interessen unserer Mandanten."}}, "team": {"lawyer1": {"name": "Dr. <PERSON>", "role": "Gründungspartner & Fachanwalt für Familienrecht", "bio": "Dr. <PERSON> ist Gründungspartner unserer Kanzlei und Experte für Familien- und Erbrecht. Mit über 20 Jahren Erfahrung und einer Erfolgsquote von 96% vertritt er Mandanten in komplexen Scheidungs- und Sorgerechtsverfahren. Als Dozent an der Universität München gibt er sein Wissen an die nächste Generation von Juristen weiter.", "expertise": {"divorce": "Scheidungsrecht", "custody": "<PERSON><PERSON><PERSON><PERSON>", "maintenance": "Unterhaltsrecht", "assets": "Vermögensauseinandersetzung"}}, "lawyer2": {"name": "Dr. <PERSON>", "role": "Partnerin & Fachanwältin für Arbeitsrecht", "bio": "Dr. <PERSON> ist spezialisiert auf Arbeits- und Vertragsrecht mit besonderem Fokus auf Kündigungsschutz und Arbeitnehmerrechte. Sie vertritt sowohl Arbeitnehmer als auch mittelständische Unternehmen und bringt 15 Jahre Erfahrung in komplexen arbeitsrechtlichen Verfahren mit.", "expertise": {"dismissal": "Kündigungsschutz", "contracts": "Arbeitsverträge", "severance": "Abfindungen", "workCouncil": "Betriebsratsrecht"}}, "lawyer3": {"name": "<PERSON>", "role": "Partner & Fachanwalt für Strafrecht", "bio": "<PERSON> ist Ihr Verteidiger in allen strafrechtlichen Angelegenheiten. Mit seiner analytischen Denkweise und umfassenden Erfahrung in der Strafverteidigung setzt er sich engagiert für die Rechte seiner Mandanten ein und hat zahlreiche Freisprüche erwirkt.", "expertise": {"economic": "Wirtschaftsstrafrecht", "defense": "Strafverteidigung", "juvenile": "Jugendstrafrecht"}}, "lawyer4": {"name": "<PERSON><PERSON><PERSON>", "role": "Fachanwältin für Verkehrsrecht", "bio": "<PERSON><PERSON><PERSON> ist Ihre Expertin für Verkehrsrecht und Unfallregulierung. Sie vertritt Mandanten bei Bußgeldverfahren, Führerscheinentzug und Schadensersatzansprüchen nach Verkehrsunfällen mit einer beeindruckenden Erfolgsquote.", "expertise": {"accident": "Unfallregulierung", "fines": "Bußgeldverfahren", "license": "Führerscheinrecht"}}, "lawyer5": {"name": "<PERSON>", "role": "Fachanwalt für Vertragsrecht", "bio": "<PERSON> ist spezialisiert auf Vertrags- und Handelsrecht. Er berät Unternehmen bei der Gestaltung und Prüfung von Verträgen sowie bei der Durchsetzung vertraglicher Ansprüche mit einem klaren Fokus auf wirtschaftliche Lösungen.", "expertise": {"contractDesign": "Vertragsgestaltung", "commercialLaw": "Handelsrecht", "termsConditions": "AGB-Recht"}}, "lawyer6": {"name": "<PERSON>", "role": "Fachanwältin für Erbrecht", "bio": "<PERSON> un<PERSON>tützt Sie bei allen Fragen rund um Testament, Erbschaft und Nachfolgeplanung. Mit ihrer einfühlsamen Art begleitet sie Mandanten durch schwierige Zeiten und findet faire Lösungen bei erbrechtlichen Auseinandersetzungen.", "expertise": {"willCreation": "Testamentsgestaltung", "inheritance": "Erbauseinandersetzung", "compulsory": "Pflichtteilsrecht"}}}, "expertise": {"familyLaw": {"title": "Familienrecht", "description": "Kompetente Beratung und Vertretung in allen familienrechtlichen Angelegenheiten, von Scheidung über Sorgerecht bis hin zu Unterhaltsfragen."}, "employmentLaw": {"title": "Arbeitsrecht", "description": "Umfassende Unterstützung bei arbeitsrechtlichen Konflikten, Kündigungsschutz und Vertragsgestaltung für Arbeitnehmer und Arbeitgeber."}, "criminalLaw": {"title": "Strafrecht", "description": "Engagierte Strafverteidigung in allen Verfahrensstadien mit Fokus auf eine optimale Verteidigungsstrategie."}, "trafficLaw": {"title": "Verkehrsrecht", "description": "Durchsetzung Ihrer Rechte bei Verkehrsunfällen, Bußgeldverfahren und Führerscheinangelegenheiten."}}}, "advantages": {"title": "UNSERE LÖSUNGEN", "subtitle": "Wie wir Ihnen helfen, erfolgreiche mobile Apps zu entwickeln", "speed": "<PERSON><PERSON><PERSON>", "speedDesc": "Wir liefern Lösungen schnell, ohne Kompromisse bei der Qualität", "stability": "Zuverlässige Anwendungen", "stabilityDesc": "Unsere Anwendungen sind auf Stabilität und Leistung ausgelegt", "cost": "Kosteneffizienz", "costDesc": "Optimierter Entwicklungsprozess spart Zeit und Geld", "timeToMarket": "Schnellere Markteinführung", "timeToMarketDesc": "Bringen Sie Ihr Produkt schnell auf den Markt und bleiben Sie der Konkurrenz voraus", "aiIntegration": "KI-Integration", "aiIntegrationDesc": "Verbessern Sie Ihr Unternehmen mit leistungsstarken KI-Funktionen", "development": "Full-Stack-Entwicklung", "developmentTime": "4-12 <PERSON><PERSON><PERSON>, variiert je nach Komplexität", "developmentDesc": "Komplette mobile Anwendungsentwicklung vom Konzept bis zur Bereitstellung, mit vollständiger Backend-Integration und erweiterten Funktionen.", "mvp": "MVP-<PERSON><PERSON><PERSON><PERSON>", "mvpTime": "2-4 <PERSON><PERSON><PERSON>, variiert je nach Komplexität", "mvpDesc": "Starten Sie schnell mit einem Minimum Viable Product, das Kernfunktionalitäten bietet, um Ihr Konzept zu validieren und frühe Nutzer oder Investoren anzuziehen.", "prototype": "Schnelles Prototyping", "prototypeTime": "1-2 <PERSON><PERSON><PERSON>, variiert je nach Komplexität", "prototypeDesc": "Testen Sie Konzepte schnell mit interaktiven Prototypen, bevor <PERSON> sich für die vollständige Entwicklung entscheiden und sparen Sie Zeit und Ressourcen.", "qa": "Qualitätssicherung", "qaTime": "Fortlau<PERSON>d, skaliert mit der Projektkomplexität", "qaDesc": "Umfassende Tests auf allen Geräten und Plattformen, um sicherzustellen, dass Ihre App mit automatisierten und manuellen Testprotokollen einwandfrei funktioniert.", "consulting": "Technische Beratung", "consultingTime": "<PERSON><PERSON>, basierend auf der Projektkomplexität", "consultingDesc": "Expertenberatung zu Technologie-Stack, Architekturentscheidungen und Implementierungsstrategien zur Optimierung Ihrer mobilen Anwendung.", "uiux": "UI/UX-Design", "uiuxTime": "2-3 <PERSON><PERSON><PERSON>, variiert je nach Komplexität", "uiuxDesc": "Nutzerzentriertes Design, das Ästhetik mit Funktionalität verbindet und intuitive und ansprechende mobile Erlebnisse schafft.", "maintenance": "Wartung & Support", "maintenanceTime": "Fortlau<PERSON>d, skaliert mit der Projektkomplexität", "maintenanceDesc": "Langfristiger Support mit regelmäßigen Updates, Leistungsoptimierung und Funktionserweiterungen, um Ihre App wettbewerbsfähig zu halten.", "analytics": "Analytics-Integration", "analyticsTime": "1-2 <PERSON><PERSON><PERSON>, variiert je nach Komplexität", "analyticsDesc": "Implementierung von Datenerfassung, um umsetzbare Einblicke in das Nutzerverhalten zu gewinnen und datengestützte Entscheidungen für Ihre App zu ermöglichen.", "training": "Team-<PERSON><PERSON><PERSON>", "trainingTime": "1-2 <PERSON><PERSON><PERSON>, variiert je nach Komplexität", "trainingDesc": "Umfassende Schulung für Ihr Team zur Wartung und Erweiterung Ihrer Anwendung nach der Übergabe.", "developmentEfficiency": "Entwicklungseffizienz", "timeToMarketReduction": "Reduzierung der Markteinführungszeit", "conceptValidation": "Konzeptvalidierung", "bugFreeRate": "Fehlerfreie Rate", "technicalImprovement": "Technische Verbesserung", "userSatisfaction": "Benutzerzufriedenheit", "appUptime": "App-Betriebszeit", "dataAccuracy": "Datengenauigkeit", "knowledgeRetention": "Wissensspeicherung", "developmentInfo": {"title": "Entwicklungszeiträume", "simpleApp": {"title": "Einfache App", "examples": "Beispiele: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, einfache Informations-A<PERSON> ohne Backend.", "features": ["<PERSON>ige Screens (3-5)", "<PERSON><PERSON> oder minimale Backend-Integration", "Standard-UI-Komponenten", "<PERSON>ine komplexen Animationen oder Funktionen"], "timeline": {"total": "Entwicklungszeit: 4-8 W<PERSON>en", "frontend": "Frontend: 2-4 <PERSON><PERSON><PERSON>", "backend": "Backend (falls benötigt): 1-2 <PERSON><PERSON><PERSON>", "testing": "Testing und Deployment: 1-2 W<PERSON>en"}}, "mediumApp": {"title": "<PERSON><PERSON><PERSON>", "examples": "Beispiele: E-Commerce-Apps, Social-Media-Apps mit grundlegenden Funktionen, Apps mit Benutzerregistrierung und Datenbankintegration.", "features": ["6-15 Screens", "Backend-Integration (z. B. REST- oder GraphQL-APIs)", "Benutzerregistrierung und Authentifizierung", "Datenbank für Benutzer- und App-Daten", "Einige Animationen und interaktive Elemente", "Push-Benachrichtigungen"], "timeline": {"total": "Entwicklungszeit: 8-16 <PERSON><PERSON><PERSON>", "frontend": "Frontend: 4-6 <PERSON><PERSON><PERSON>", "backend": "Backend: 3-5 <PERSON><PERSON><PERSON>", "testing": "Testing und Deployment: 2-3 <PERSON><PERSON><PERSON>"}}, "complexApp": {"title": "Komplexe App", "examples": "Beispiele: <PERSON><PERSON> wie Uber, Instagram, oder Banking-Apps mit erweiterten Funktionen.", "features": ["15+ Screens", "Hochgradig interaktive Benutzeroberfläche", "Echtzeit-Funktionen (z. B. Live-Tracking, Chat)", "<PERSON> von <PERSON>-APIs (z. B. Zahlungs-Gateways, Karten-APIs)", "<PERSON><PERSON><PERSON><PERSON><PERSON> Backend mit Cloud-Integration", "Sicherheitsfunktionen (z. B. Verschlüsselung, Zwei-Faktor-Authentifizierung)", "Offline-Funktionalität"], "timeline": {"total": "Entwicklungszeit: 16-32 Wochen oder länger", "frontend": "Frontend: 6-10 <PERSON><PERSON><PERSON>", "backend": "Backend: 6-12 <PERSON><PERSON><PERSON>", "testing": "Testing und Deployment: 4-6 <PERSON><PERSON><PERSON>"}}, "factors": {"title": "<PERSON><PERSON><PERSON><PERSON>, die die Entwicklungszeit beeinflussen", "teamSize": "Teamgröße: Ein größeres Team (z. B. separate Entwickler für Frontend, Backend, und QA) kann die Entwicklung beschleunigen. Ein einzelner Entwickler benötigt mehr Zeit.", "technology": "Technologie: Native Entwicklung (z. <PERSON><PERSON> Swift für iOS, <PERSON><PERSON><PERSON> für Android) dauert oft länger als plattformübergreifende Ansätze wie Flutter oder React Native. Plattformübergreifende Frameworks können die Entwicklungszeit um 30-40 % reduzieren.", "requirements": "Anforderungen und Änderungen: Häufige Änderungen oder unklare Anforderungen können die Entwicklungszeit verlängern.", "testing": "Testing und Debugging: Komplexe Apps erfordern mehr Zeit für Tests, insbesondere bei mehreren Plattformen (iOS und Android).", "design": "Design: Einfache Designs benötigen weniger Zeit, während maßgeschneiderte, animierte Designs die Entwicklungszeit erhöhen."}, "summary": "Zusammenfassung: Einfache App: 4-8 Wochen. Mittlere App: 8-16 Wochen. Komplexe App: 16-32 Wochen oder länger.", "aiComparison": "Mit unserer KI-gestützten Entwicklung und Flutter können wir diese Zeiträume um 40-60% reduzieren, während wir hohe Qualität und Leistung beibehalten."}}, "serviceSection": {"title": "Unsere Dienstleistungen", "subtitle": "Umfassende Immobilienlösungen", "description": "Von der Suche nach Ihrem Traumhaus bis zum Verkauf Ihrer Immobilie zu einem Premium-Wert bieten wir ein vollständiges Spektrum an Immobiliendienstleistungen, die auf Ihre individuellen Bedürfnisse zugeschnitten sind und Ihre Erwartungen übertreffen werden.", "viewAll": "Ihr Projekt besprechen", "service1": {"title": "Wohnimmobilien", "description": "Finden Sie Ihr Traumhaus oder verkaufen Sie Ihre Immobilie mit unserem erfahrenen Wohnimmobilien-Service. Wir kümmern uns um alles von der Inserat-Erstellung bis zum Vertragsabschluss mit Professionalität und Sorgfalt.", "items": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Personalisierte Immobiliensuche basierend auf Ihren spezifischen Bedürfnissen und Vorlieben"}, {"name": "<PERSON><PERSON>ver<PERSON><PERSON>", "description": "Strategische Preisgestaltung, professionelle Fotografie und gezieltes Marketing, um qualifizierte Käufer anzuziehen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Detaillierte Immobilienbewertungen und Markttrend-<PERSON><PERSON>sen, um Ihre Investition zu maximieren"}, {"name": "Verhandlung", "description": "Expertive Verhandlungsskills, um die bestmöglichen Bedingungen und Preise für Ihre Transaktion zu sichern"}]}, "webDev": {"title": "Webentwicklung", "description": "Moderne, responsive Websites und Webanwendungen, die mit modernsten Technologien wie Next.js, React und Node.js entwickelt werden.", "benefits": ["SEO-optimierte Leistung mit serverseitigem Rendering", "Responsive Designs, die auf allen Geräten funktionieren", "Skalierbare Architektur für zukünftiges Wachstum"]}, "uiuxDesign": {"title": "UI/UX-Design", "description": "Intuitive, nutzerzentrierte Designs, die Kunden begeistern und das Engagement durch durchdachte Benutzererfahrungen verbessern.", "benefits": ["Nutzerzentrierter Design-Ansatz", "Konversionsoptimierte Schnittstellen", "Konsistente Markenerfahrung auf allen Plattformen"]}, "consulting": {"title": "Technische Beratung", "description": "Expertenberatung zu Technologiestrategie und -implementierung, um Ihnen zu helfen, fundierte Entscheidungen für Ihre digitalen Projekte zu treffen.", "benefits": []}, "service5": {"title": "Gewerbeimmobilien", "description": "Spezialisierte Expertise in Gewerbeimmobilientransaktionen, die Unternehmen dabei hilft, den perfekten Standort für ihren Betrieb zu finden oder Investoren dabei unterstützt, die Rendite auf Gewerbeimmobilien zu maximieren.", "items": [{"name": "Büroflächen", "description": "Finden der idealen Bürofläche, die Standort, Ausstattung und Budget für Ihre geschäftlichen Anforderungen in Einklang bringt"}, {"name": "Einzelhandelsstandorte", "description": "Strategische Standortwahl für Einzelhandelsunternehmen zur Maximierung von Kundenfrequenz und Kundenbindung"}, {"name": "Industrieimmobilien", "description": "Beschaf<PERSON><PERSON> von <PERSON>ger<PERSON>en, Produktionsstätten und Vertriebszentren mit optimalen logistischen Überlegungen"}, {"name": "Anlageimmobilien", "description": "Analyse und Erwerb von ertragsstarken Gewerbeimmobilien mit hohem ROI-Potenzial"}]}, "service6": {"title": "Immobilienverwaltung", "description": "Umfassende Immobilienverwaltungsdienste für Investoren und Vermieter, die jeden Aspekt des Mietobjektbesitzes handhaben, um die Renditen zu maximieren und gleichzeitig den Stress zu minimieren.", "items": [{"name": "Mieterverwaltung", "description": "Komplettes Lebenszyklus-<PERSON> von Mi<PERSON>n, vom Screening bis zur Auszugsinspektion"}, {"name": "Instandhaltungskoordination", "description": "Prompte Bearbei<PERSON><PERSON> von Reparaturen und Wartung mit unserem Netzwerk vertrauenswürdiger Auftragnehmer"}, {"name": "Finanzberichterstattung", "description": "Regelmäßige Finanzberichte und umfassende Berichterstattung über die Immobilienperformance"}, {"name": "Mieteinzug", "description": "Zuverlässige Mieteinzugssysteme mit Online-Zahlungsoptionen und Nachverfolgung von verspäteten Zahlungen"}]}}, "services": {"title": "Unsere Technologie-Stack", "subtitle": "Umfassende digitale Lösungen", "description": "Wir nutzen modernste Technologien in verschiedenen Bereichen, um robuste und skalierbare Lösungen für Ihre geschäftlichen Anforderungen zu liefern.", "frontend": "Frontend-Entwicklung", "frontendDesc": "Entwicklung responsiver und interaktiver Benutzeroberflächen mit modernen Web-Technologien", "backend": "Backend-Entwicklung", "backendDesc": "Erstellung robuster serverseitiger Lösungen und APIs für skalierbare Anwendungen", "mobile": "Mobile Entwicklung", "mobileDesc": "Entwicklung plattformübergreifender mobiler Anwendungen mit nativer Leistung", "ai": "KI & Maschinelles Lernen", "aiDesc": "Integration intelligenter Funktionen und Automatisierung in Anwendungen", "mobileApps": "Mobile App-Entwicklung", "mobileAppsDesc": "Plattformübergreifende Anwendungen mit Flutter für iOS und Android", "webDev": "Webentwicklung", "webDevDesc": "Moderne, responsive Websites und Webanwendungen", "uiuxDesign": "UI/UX-Design", "uiuxDesignDesc": "Intuitive, benutzerzentrierte Designs, die Kunden begeistern", "consulting": "Technische Beratung", "consultingDesc": "Expertenberatung zu Technologiestrategie und Implementierung", "aiSolutions": "KI-Integration", "aiSolutionsDesc": "Integrieren Sie KI-Funktionen zur Verbesserung Ihres Unternehmens", "viewAll": "Alle Dienstleistungen anzeigen"}, "prices": {"title": "Immobilienwert-Rechner", "subtitle": "Schätzen Sie den Wert Ihrer Immobilie", "description": "Nutzen Sie unseren Immobilienwertrechner, um eine ungefähre Marktbewertung Ihres Hauses basierend auf Schlüsselfaktoren wie Lage, Größe und Ausstattung zu erhalten.", "metaTitle": "Interaktiver <PERSON> | Erhalten Sie Ihr individuelles Angebot", "metaDescription": "Nutzen Si<PERSON> unseren interaktiven Preisrechner, um ein individuelles Angebot zu erhalten, das auf Ihre Projektbedürfnisse zugeschnitten ist. Transparente Preise ohne versteckte Gebühren.", "caseStudyTitle": "Fallstudie: 40% schnellere Markteinführung", "caseStudyDescription": "Unser Fintech-Kunde aus Wyoming brachte sein MVP 40% schneller als der Branchendurchschnitt auf den Markt, was ihm zusätzliche Finanzierung und beschleunigtes Wachstum ermöglichte.", "promotionTitle": "Bündeln Sie Dienstleistungen & sparen Sie 15%", "promotionDescription": "Kombinieren Sie zwei oder mehr Dienstleistungen und erhalten Sie 15% Rabatt auf Ihre gesamten Projektkosten.", "calculatorTitle": "Benötigen Sie ein individuelles Angebot?", "calculatorDescription": "Nutzen Sie unseren interaktiven Preisrechner, um eine detaillierte Schätzung zu erhalten, die auf Ihre spezifischen Projektanforderungen zugeschnitten ist.", "calculatorButton": "Testen Si<PERSON> unseren Preisrechner", "discussButton": "Besprechen Sie Ihr Projekt", "contactButton": "Kontaktieren Sie uns", "calculator": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Erhalten Sie ein individuelles Angebot für Ihre Bedürfnisse", "back": "Zurück", "selectServiceType": "Dienstleistungstyp auswählen", "stepIndicator": "<PERSON><PERSON><PERSON>", "continueButton": "<PERSON><PERSON>", "getQuoteButton": "Ang<PERSON><PERSON> erhalten", "startOver": "<PERSON>eu starten", "calculatorTypes": {"mvp": {"name": "MVP-<PERSON><PERSON><PERSON><PERSON>", "description": "Berechnen Sie die Kosten für die Entwicklung Ihres Minimum Viable Product", "tagline": "Bringen Sie Ihre App schnell mit Kernfunktionen auf den Markt"}, "prototype": {"name": "Schneller Prototyp", "description": "Schätzen Sie Ihre Prototyp-Entwicklungskosten", "tagline": "Testen Sie Ihre Idee mit einem funktionalen Prototyp"}, "homepage": {"name": "Homepage & Landing Page", "description": "Erhalten Sie Preise für Ihre Website oder Landing Page", "tagline": "Erstellen Sie eine professionelle Online-Präsenz"}, "consulting": {"name": "Technische Beratung", "description": "Schätzen Sie Beratungsstunden und -pakete", "tagline": "Expertenberatung für Ihre technischen Herausforderungen"}}, "mvpCalculator": {"title": "MVP-Entwick<PERSON>s<PERSON>chner", "subtitle": "Konfigurieren Sie Ihre MVP-Anforderungen für ein individuelles Angebot", "industry": "Wählen Sie Ihre Branche", "timeline": "Entwicklungszeitplan", "features": "App-Funktionen auswählen", "featuresInfo": "<PERSON><PERSON>hlen Sie die Funktionen, die Sie für Ihr <strong>voll funktionsfähiges, App Store-bereites</strong> MVP ben<PERSON><PERSON><PERSON>. Dies sind Ihre Kernfunktionen.", "addFeatures": "<PERSON><PERSON><PERSON>, um Funktionen hinzuzufügen...", "customFeatures": "Zusätzliche Anforderungen", "customFeaturesPlaceholder": "Beschreiben Sie alle benutzerdefinierten Funktionen oder Anforderungen...", "needsReview": "Ich möchte eine Code-Überprüfung meines bestehenden Projekts", "industries": {"ecommerce": "E-Commerce", "health": "Gesundheitswesen", "finance": "Fin<PERSON>zen", "social": "Soziale Medien", "productivity": "Produktivität", "other": "<PERSON><PERSON>"}, "timelines": {"normal": {"label": "Standard (4-5 Wochen)", "description": "Reguläres Entwicklungstempo"}, "fast": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (3-4 W<PERSON>en)", "description": "30% schnellere Lieferung"}, "urgent": {"label": "Priorität (2-3 Wochen)", "description": "Dediziertes Team, schnellste Lieferung"}}, "featureCategories": {"auth": "Authentifizierung", "user": "Benutzerverwaltung", "common": "Allgemeine Funktionen", "payment": "Zahlung", "device": "Gerätefunktionen", "data": "Daten & Speicher", "social": "Soziale Funktionen"}}, "prototypeCalculator": {"title": "Schneller Prototyp-<PERSON><PERSON>ner", "subtitle": "Konfigurieren Sie Ihre Prototyp-Anforderungen für einen visuellen und navigierbaren Prototyp", "uiOnly": "Nur UI", "timeline": "Entwicklungszeitplan", "complexity": "Prototyp-Komplexität", "userRoles": "Anzahl der Benutzerrollen", "userRolesBasic": "Einfach (1)", "userRolesComplex": "Komplex (5+)", "userRolesExamples": "Beispiele: <PERSON><PERSON><PERSON>, Administrator, Editor, <PERSON><PERSON><PERSON><PERSON>, Moderator", "features": "Prototyp-Funktionen auswählen", "featuresInfo": "<PERSON>ählen Sie die Funktionen, die Sie für Ihren visuellen Prototyp benötigen. Diese definieren den Umfang Ihres Prototyps.", "addFeatures": "<PERSON><PERSON><PERSON>, um Funktionen hinzuzufügen...", "integrations": "UI-Integrationen", "integrationsInfo": "<PERSON><PERSON>hlen Sie die UI-Integrationen, die in Ihren Prototyp aufgenommen werden sollen.", "addIntegrations": "<PERSON><PERSON><PERSON>, um Integrationen hinzuzufügen...", "customFeatures": "Zusätzliche Anforderungen", "customFeaturesPlaceholder": "Beschreiben Sie alle benutzerdefinierten Funktionen oder Anforderungen...", "needsReview": "Ich möchte eine Design-Überprüfung meiner bestehenden Mockups", "flutterPrototype": "Flutter-Powered Prototyp", "flutterInfo": "Unsere Flutter-Prototypen bieten überlegene Leistung und können später leicht in ein vollständiges MVP umgewandelt werden.", "complexityLevels": {"simple": {"label": "<PERSON><PERSON><PERSON>", "description": "Grundlegende Benutzerflüsse, minimale Bildschirme"}, "medium": {"label": "<PERSON><PERSON><PERSON>", "description": "Mehrere Benutzerflüsse, Standardbildschirme"}, "complex": {"label": "Komplex", "description": "Fortgeschrittene UI, viele Bildschirme"}}, "timelines": {"normal": {"label": "Standard (1-2 Wochen)", "description": "Reguläres Entwicklungstempo"}, "fast": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (5-7 Tage)", "description": "30% schnellere Lieferung"}, "urgent": {"label": "Priorität (3-4 Tage)", "description": "Dediziertes Team, schnellste Lieferung"}}}, "homepageCalculator": {"title": "Homepage & Landing Page Rechner", "subtitle": "Konfigurieren Sie Ihre Website-Anforderungen für ein individuelles Angebot", "pageCount": "Anzahl der Seiten", "pageCountLanding": "<PERSON> Page (1)", "pageCountFull": "Vollständige Website (15+)", "pageCountExamples": "Beispielseiten: Startseite, Über uns, Dienstleistungen, Portfolio, Kontakt, Blog, usw.", "timeline": "Entwicklungszeitplan", "features": "Website-Funktionen", "featuresInfo": "Wählen Sie die Funktionen, die Sie für Ihre <strong>responsive Website</strong> benötigen. Erforderliche Funktionen können nicht abgewählt werden.", "addFeatures": "<PERSON><PERSON><PERSON>, um Funktionen hinzuzufügen...", "seo": "Suchmaschinenoptimierung", "seoDescription": "Grundlegende SEO-Einrichtung (Meta-Tags, Sitemap, usw.)", "multilingual": "Mehrsprachige Unterstützung", "multilingualDescription": "Unterstützung für mehrere Sprachen", "customFeatures": "Zusätzliche Anforderungen", "customFeaturesPlaceholder": "Beschreiben Sie alle benutzerdefinierten Funktionen oder Anforderungen...", "needsReview": "Ich möchte eine Überprüfung meiner bestehenden Website", "timelines": {"normal": {"label": "Standard (2-3 Wochen)", "description": "Reguläres Entwicklungstempo"}, "fast": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (1-2 Wochen)", "description": "30% schnellere Lieferung"}, "urgent": {"label": "Priorität (3-5 Tage)", "description": "Dediziertes Team, schnellste Lieferung"}}}, "consultingCalculator": {"title": "Technischer Beratungsrechner", "subtitle": "Konfigurieren Sie Ihre Beratungsanforderungen für ein individuelles Angebot", "projectPhase": "Projektphase", "teamSize": "Teamgröße", "teamSizeSolo": "Solo (1)", "teamSizeLarge": "Großes Team (10+)", "developer": "<PERSON><PERSON><PERSON><PERSON>", "developers": "<PERSON><PERSON><PERSON><PERSON>", "expertise": "Benötigte Expertise", "addExpertise": "<PERSON><PERSON><PERSON>, um Expertisebereiche hinzuzufügen...", "duration": "Gesch<PERSON><PERSON><PERSON>", "durationShort": "Kurzfristig (1 Woche)", "durationLong": "<PERSON><PERSON><PERSON><PERSON> (12+ <PERSON><PERSON><PERSON>)", "week": "<PERSON><PERSON><PERSON>", "weeks": "<PERSON><PERSON><PERSON>", "expertConsultation": "Experten-Technische Beratung", "consultationDescription": "Unsere technischen Beratungsdienste nutzen jahrzehntelange Branchenerfahrung in verschiedenen Technologien und Branchen. Wir bieten sowohl strategische Beratung als auch praktische Implementierungsunterstützung.", "ctoLevel": "CTO-Level Expertise", "flexibleScheduling": "Flexible Terminplanung", "projectPhases": {"planning": "Planung & Strategie", "development": "Aktive Entwicklung", "maintenance": "Wartung & Support", "optimization": "Leistungsoptimierung"}}, "leadForm": {"title": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> Ihr {calculatorType}-Ang<PERSON><PERSON>", "subtitle": "<PERSON><PERSON>llen Sie das Formular aus, um ein detailliertes Angebot per E-Mail zu erhalten", "fullName": "Vollständiger Name", "namePlaceholder": "<PERSON>", "nameRequired": "Name ist erforderlich", "email": "E-Mail-Adresse", "emailPlaceholder": "<EMAIL>", "emailRequired": "E-Mail ist erforderlich", "emailInvalid": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "company": "Firmenname", "companyOptional": "(Optional)", "companyPlaceholder": "Muster GmbH", "agreeTerms": "<PERSON>ch bin damit ein<PERSON>den, das Preisangebot per E-Mail zu erhalten", "secureInfo": "Ihre Daten sind sicher und werden nicht weitergegeben", "termsRequired": "Sie müssen den Bedingungen zustimmen", "getQuote": "Ang<PERSON><PERSON> erhalten"}, "pricingResult": {"title": "<PERSON><PERSON> individ<PERSON><PERSON>", "subtitle": "Basierend auf Ihren Anforderungen", "estimatedCost": "Geschätzte Kosten", "estimatedTime": "Geschätzter Zeitrahmen", "weeks": "<PERSON><PERSON><PERSON>", "days": "Tage", "hours": "Stunden", "startingFrom": "Ab", "hourlyRate": "Stundensatz", "includedFeatures": "Enthaltene Funktionen", "nextSteps": "Nächste Schritte", "nextStepsDescription": "Wir haben ein detailliertes Angebot an Ihre E-Mail gesendet. Unser Team wird sich in Kürze mit Ihnen in Verbindung setzen, um Ihr Projekt im Detail zu besprechen.", "startOver": "<PERSON>eu starten", "contactUs": "Kontaktieren Sie uns"}}, "packages": {"mvp": {"title": "MVP-<PERSON><PERSON><PERSON><PERSON>", "timeframe": "3-4 W<PERSON>en", "description": "Bringen Sie Ihre Idee schnell mit einem Minimum Viable Product auf den Markt", "features": ["Implementierung der Kernfunktionalität", "Grundlegendes UI-Design", "Benutzerauthentifizierung", "Datenspeicherlösung", "Bereitstellung auf einer Plattform", "Worldwide-Compliance-Ready"]}, "prototype": {"title": "Schneller Prototyp", "timeframe": "1-2 Wochen", "description": "Testen Sie Ihr Konzept mit einem funktionalen Prototyp", "features": ["Interaktive UI-Mockups", "Grundlegende Funktionalität", "Implementierung des Benutzerflusses", "Präsentation für Stakeholder", "Flutter-gestützte MVP-Entwicklung"]}, "architecture": {"title": "Projektarchitektur", "timeframe": "1-2 Wochen", "description": "Solide Grundlage für den Erfolg Ihres Projekts", "features": ["Technische Spezifikationen", "System-Architekturdesign", "Datenbankschema", "API-Dokumentation", "Entwicklungs-Roadmap"]}, "consulting": {"title": "Technische Beratung", "timeframe": "Fortlaufend", "description": "Expertenberatung für Ihre technischen Entscheidungen", "features": ["Empfehlungen zum Technologie-Stack", "Code-Reviews", "Leistungsoptimierung", "Sicherheitsbewertung", "Skalierbarkeitsplanung", "UAE-Zahlungsgateway-Integration"]}}}, "packages": {"title": "Unsere Pakete", "subtitle": "Maßgeschneiderte Lösungen für Ihre Bedürfnisse", "mvp": {"title": "MVP-<PERSON><PERSON><PERSON><PERSON>", "timeframe": "3-4 W<PERSON>en", "description": "Bringen Sie Ihre Idee schnell mit einem Minimum Viable Product auf den Markt", "features": ["Implementierung der Kernfunktionalität", "Grundlegendes UI-Design", "Benutzerauthentifizierung", "Datenspeicherlösung", "Deployment auf einer Plattform"]}, "prototype": {"title": "Prototyp-Paket", "timeframe": "1-2 Wochen", "description": "Testen Sie Ihr Konzept mit einem funktionalen Prototyp", "features": ["Interaktive UI-Mockups", "Grundlegende Funktionalität", "Implementierung des Benutzerflusses", "Präsentation für Stakeholder"]}, "architecture": {"title": "Projektarchitektur", "timeframe": "1-2 Wochen", "description": "Solide Grundlage für den Erfolg Ihres Projekts", "features": ["Technische Spezifikationen", "System-Architekturdesign", "Datenbankschema", "API-Dokumentation", "Entwicklungs-Roadmap"]}, "consulting": {"title": "Technische Beratung", "timeframe": "Fortlaufend", "description": "Expertenberatung für Ihre technischen Entscheidungen", "features": ["Empfehlungen zum Technologie-Stack", "Code-Reviews", "Leistungsoptimierung", "Sicherheitsbewertung", "Skalierbarkeitsplanung"]}}, "solutionsPortfolio": {"title": "Ausgewählte Immobilien", "subtitle": "Außergewöhnliche Immobilienangebote", "clickInstruction": "Klicken Sie auf die Immobilie für Details & mehr Fotos", "imageCounter": "Bild {current} von {total}", "keyFeatures": "Hauptfunktionen", "problemsSolved": "Gelöste Probleme", "viewDetails": "Details anzeigen", "screenshot": "Screenshot", "screenshots": "Screenshots", "categories": {"all": "Alle Kategorien", "aiAssistant": "KI-Assistent", "foodDelivery": "Essenslieferung", "hospitality": "Gastgewerbe", "medical": "Medizin", "lifestyle": "Lifestyle-Apps", "automotive": "Automobil"}, "items": {"spotzAiAssistant": {"title": "Spotz KI-Assistent", "description": "KI-gestützter Assistent zum Finden von Orten in der Nähe, Empfehlungen geben, Navigation und Notfalldienste bereitstellen.", "imageAlt": "Spotz KI-Assistent mobile App Interface Screenshot {index}"}, "foodDelivery": {"title": "Essenslieferung", "description": "Komplette Lösung für Essensentdeckung und -lieferung mit intuitivem Bestellprozess.", "imageAlt": "Essenslieferungs-App Screenshot {index}"}, "hostIQ": {"title": "HostIQ - Hospitality SuperApp", "description": "All-in-One-Lösung für Hotels zur Verwaltung von Betriebsabläufen, Gästeerfahrungen und Dienstleistungen.", "imageAlt": "HostIQ Hospitality-Management-Plattform Screenshot {index}"}, "businessManagement": {"title": "Lokales Geschäftsmanagement", "description": "Marketing-Tools, Veranstaltungsplanung, Story-Sharing und Reservierungsverwaltung für lokale Unternehmen.", "imageAlt": "Geschäftsmanagement-Anwendung Screenshot {index}"}, "lumeusApp": {"title": "Lumeus A<PERSON>", "description": "Soziale Netzwerk- und Verbindungsplattform mit erweiterten Funktionen.", "imageAlt": "Lumeus Social-Networking-App Screenshot {index}"}, "nearby": {"title": "In der Nähe", "description": "Standortbasierte Entdeckungs-App, um alles in Ihrer Umgebung zu finden.", "imageAlt": "In der Nähe App Interface Screenshot {index}"}, "toggCarControl": {"title": "Togg Fahrzeugsteuerung", "description": "Intelligentes Fahrzeugsteuerungs- und Managementsystem für Togg-Fahrzeuge.", "imageAlt": "Togg Fahrzeugsteuerungs-App Screenshot {index}"}, "socialMediaPlatform": {"title": "Social-Media-Plattform", "description": "Moderne Social-Media-Plattform mit ansprechender Benutzererfahrung.", "imageAlt": "Social-Media-Plattform Interface Screenshot {index}"}, "default": {"imageAlt": "Portfolio-Element Screenshot {index}"}}, "solutions": {"luxuryApartment": {"title": "Luxus-Penthouse - Innenstadt", "description": "Atemberaubendes Penthouse-Apartment mit Panoramablick auf die Stadt und erstklassiger Ausstattung.", "features": ["Verarbeitung natürlicher Sprache", "Kontextverständnis", "S<PERSON><PERSON>kennung", "Multi-Plattform-Unterstützung", "Integrationsfähigkeiten"], "problemsSolved": ["Informationszugriff", "Entscheidungsunterstützung", "Automatisierung des Kundenservice", "Aufgabenverwaltung"]}, "foodDelivery": {"title": "Essenslieferungs-Plattformen", "description": "End-to-End-Systeme für Restaurants und Lieferdienste.", "features": ["Bestellverwaltung", "Echtzeit-Tracking", "Zahlungsabwicklung", "Restaurant-Dashboard", "Lieferoptimierung"], "problemsSolved": ["Restaurant-Entdeckung", "Liefer<PERSON><PERSON>", "Auftragsabwicklung", "Kundenbindung"]}, "hospitality": {"title": "Hospitality-Management-Lösungen", "description": "Digitale Systeme zur Verbesserung der Gästeerfahrung und Optimierung der Betriebsabläufe.", "features": ["Buchungsverwaltung", "Gästeservices", "Personalkoordination", "Bestandskontrolle", "Analyse-Dashboard"], "problemsSolved": ["Optimierung der Gästeerfahrung", "Betriebliche Effizienz", "Ressourcenmanagement", "Servicebereitstellung"]}, "business": {"title": "Geschäftsmanagement-Systeme", "description": "Umfassende Plattformen zur Verwaltung von Betriebsabläufen und Wachstum.", "features": ["CRM-Integration", "Bestandsverwaltung", "Finanzverfolgung", "Mitarbeiterverwaltung", "Berichtswerkzeuge"], "problemsSolved": ["Prozessoptimierung", "Datenverwaltung", "Ressourcenzuweisung", "Business Intelligence"]}, "social": {"title": "Social-Media-Plattformen", "description": "Ansprechende Social-Networking-Lösungen für Gemeinschaften.", "features": ["Benutzerprofile", "<PERSON><PERSON>e teilen", "Nachrichtensystem", "Benachrichtigungsengine", "Analyse-Tools"], "problemsSolved": ["Aufbau digitaler Gemeinschaften", "Benutzerbindung", "Inhaltsentdeckung", "Soziale Interaktion"]}, "automotive": {"title": "Automobiltechnologie-Lösungen", "description": "Digitale Schnittstellen und Steuerungssysteme für Fahrzeuge.", "features": ["Fahrzeugverwaltung", "Navigationssysteme", "Diagnosewerkzeuge", "Entertainment-Integration", "IoT-Konnektivität"], "problemsSolved": ["Fahrzeugsteuerung", "Navigationsherausforderungen", "Wartungsmanagement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}}, "portfolio": {"title": "Unsere Expertise", "subtitle": "Branchen & Problemlösungen", "all": "Alle Branchen", "screenshot": "Screenshot", "screenshots": "Screenshots", "problemsWeSolve": "<PERSON><PERSON>, die wir lösen", "noSectorsFound": "Keine Branchen für den ausgewählten Filter gefunden.", "categories": {"aiAssistant": "KI-Assistent", "foodDelivery": "Lebensmittellieferung", "hospitality": "Gastgewerbe", "business": "Geschäft", "social": "Soziale Medien", "automotive": "Automobilindustrie"}, "sectors": {"assistant": "Assistenz-Apps", "food": "Essensbestellung & Lieferung", "hospitality": "Gastgewerbe", "lifestyle": "Lifestyle-Apps", "social": "Soziale Medien", "automotive": "Automobilindustrie", "medical": "Medizin & Gesundheitswesen", "business": "Geschäftslösungen"}, "sectorDescriptions": {"assistant": "KI-gestützte Assistenten, die die Produktivität steigern und personalisierte Empfehlungen geben", "food": "Nahtlose E<PERSON>sbestellungs- und Lieferplattformen mit Echtzeit-Tracking", "hospitality": "Digitale Lösungen für Hotels und Gastgewerbe zur Verbesserung des Gästeerlebnisses", "lifestyle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, die den Alltag, das Wohlbefinden und die persönliche Entwicklung verbessern", "social": "Plattformen, die Menschen und Gemeinschaften durch gemeinsame Interessen verbinden", "automotive": "Intelligente Lösungen für Fahrzeugmanagement, Navigation und Fahrerassistenz", "medical": "Digitale Gesundheitslösungen, die die Patientenversorgung und medizinischen Abläufe verbessern", "business": "Unternehmensanwendungen, die Abläufe rationalisieren und die Produktivität steigern"}, "problems": {"assistant": {"1": "Informationsüberflutung", "2": "Aufgabenmanagement", "3": "Entscheidungsunterstützung"}, "food": {"1": "Bestellverwaltung", "2": "Liefer<PERSON><PERSON>", "3": "Restaurantentdeckung"}, "hospitality": {"1": "Gästemanagement", "2": "Serviceoptimierung", "3": "Buchungssysteme"}, "lifestyle": {"1": "Gesundheitstracking", "2": "Gewohnheitsbildung", "3": "Persönliche Organisation"}, "social": {"1": "Benutzerengagement", "2": "Inhaltsentdeckung", "3": "Gemeinschaftsaufbau"}, "automotive": {"1": "Fahrzeugüberwachung", "2": "Navigationoptimierung", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "medical": {"1": "Patientenmanagement", "2": "Gesundheitsüberwachung", "3": "Medizinische Aufzeichnungssysteme"}, "business": {"1": "Arbeitsablaufoptimierung", "2": "Datenmanagement", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "viewDetails": "Details anzeigen", "viewAllProjects": "Alle Projekte anzeigen"}, "clients": {"title": "<PERSON>ser<PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON>, mit denen wir zusammengearbeitet haben", "visitWebsite": "Website besuchen"}, "testimonials": {"title": "Was unsere Mandanten sagen", "subtitle": "Kundenbewertungen", "description": "<PERSON><PERSON><PERSON><PERSON>, was unsere Mandanten über ihre Erfahrungen mit unserer Kanzlei berichten.", "readMore": "<PERSON><PERSON> lesen", "readLess": "<PERSON><PERSON> anzeigen", "formTitle": "Teilen Sie Ihre Erfahrung", "name": "Name", "email": "E-Mail", "yourName": "Ihr Name", "yourEmail": "Ihre E-Mail", "caseType": "Rechtsgebiet", "rating": "Bewertung", "stars": "<PERSON><PERSON>", "yourExperience": "<PERSON><PERSON><PERSON>", "experienceDescription": "Beschreiben Sie Ihre Erfahrung mit unserer Kanzlei...", "submitReview": "Bewertung senden", "selectOption": "Bitte wählen Sie", "familyLaw": "Familienrecht", "employmentLaw": "Arbeitsrecht", "criminalLaw": "Strafrecht", "trafficLaw": "Verkehrsrecht", "contractLaw": "Vertragsrecht"}, "contact": {"title": "Kontakt", "subtitle": "Nehmen Sie Kontakt auf", "description": "Kontaktieren Sie uns für Anfragen, Projektbesprechungen oder um einen Beratungstermin zu vereinbaren. Wir helfen Ihnen gerne weiter.", "name": "Name", "email": "E-Mail", "phone": "Telefon", "message": "Nachricht", "send": "Nachricht senden", "yourName": "Ihr Name", "yourEmail": "Ihre E-Mail", "subject": "<PERSON><PERSON><PERSON>", "howCanIHelp": "Wie kann ich Ihnen helfen?", "yourMessageHere": "<PERSON><PERSON><PERSON><PERSON> hier", "getInTouch": "Kontakt aufnehmen", "sendMessage": "Nachricht senden", "schedule": "<PERSON><PERSON><PERSON> vere<PERSON>en", "freeConsultation": "Buchen Sie eine kostenlose 15-minütige Beratung", "location": "<PERSON><PERSON>", "submitButton": "<PERSON><PERSON><PERSON><PERSON>", "sending": "Wird gesendet...", "messageSent": "<PERSON><PERSON><PERSON>t gesendet!", "errorTryAgain": "<PERSON><PERSON>, bitte versuchen Sie es erneut", "orSchedule": "Oder vereinbaren Sie direkt einen Termin über den Kalender-Link", "whatsapp": "WhatsApp", "linkedin": "LinkedIn", "github": "GitHub", "booking": "<PERSON><PERSON><PERSON> buchen", "directions": "Wegbeschreibung"}, "footer": {"copyright": "© 2025 Firmenname. Alle Rechte vorbehalten.", "description": "Wir entwickeln anspruchsvolle Mobile- und Web-Anwendungen, die Unternehmen durch innovative Technologie transformieren.", "quickLinks": "Schnelllinks", "footerContact": "Kontakt", "legal": "Rechtliches", "newsletter": "Newsletter", "newsletterDesc": "Abonnieren Sie unseren Newsletter, um Updates und Einblicke zu erhalten.", "emailPlaceholder": "Ihre E-Mail-Adresse", "subscribe": "Abonnieren", "builtWith": "Erstellt mit", "and": "und", "downloadCV": "<PERSON><PERSON>", "englishCV": "<PERSON><PERSON><PERSON>", "germanCV": "De<PERSON>ch", "contactUs": "Kontaktieren Sie uns", "openingHours": "Öffnungszeiten:", "weekdayHours": "Montag - Freitag: 9:00 - 18:00 Uhr", "weekendHours": "Wochenende: Geschlossen", "appointmentsNote": "Termine außerhalb der Öffnungszeiten nach Vereinbarung möglich", "stayInformed": "Bleiben Sie informiert über rechtliche Entwicklungen", "legalUpdates": "Abonnieren Sie unseren Newsletter für aktuelle Gesetzesänderungen, Rechtsprechung und Expertentipps.", "privacyNote": "Wir respektieren Ihre Privatsphäre. Abmeldung jederzeit möglich."}, "featuresSection": {"features": [{"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> und <PERSON><PERSON>.", "icon": "IconTerminal2"}, {"title": "Einfache Bedienung", "description": "<PERSON><PERSON> ist so einfach wie die Benutzung eines Apple-Geräts und so teuer wie der Kauf eines.", "icon": "IconEaseInOut"}, {"title": "Einzigartige Preisgestaltung", "description": "Unsere Preise sind die besten auf dem Markt. <PERSON><PERSON>, keine <PERSON>, keine K<PERSON>it<PERSON> er<PERSON>.", "icon": "Icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "100% Verfügbarkeitsgarantie", "description": "Uns kann einfach niemand vom Netz nehmen.", "icon": "IconCloud"}, {"title": "Multi-Tenant-Architektur", "description": "<PERSON>e können einfach Passwörter teilen, anstatt neue Lizenzen zu kaufen.", "icon": "IconRouteAltLeft"}, {"title": "24/7 Kundensupport", "description": "Wir sind zu 100% der Zeit verfügbar. Zumindest unsere KI-Agenten.", "icon": "IconHelp"}, {"title": "Geld-zurück-Garantie", "description": "<PERSON>n Ihnen EveryAI nicht gef<PERSON>, überzeugen wir <PERSON><PERSON>, uns zu mögen.", "icon": "IconAdjustmentsBolt"}, {"title": "Und alles andere", "description": "Mir sind gerade die Textideen ausgegangen. Akzeptieren Sie meine aufrichtige Entschuldigung.", "icon": "IconHeart"}]}, "cookies": {"title": "Cookie-Einwilligung", "description": "Wir verwenden <PERSON>, um Ihr Surferlebnis zu verbessern, personalisierte Anzeigen oder Inhalte bereitzustellen und unseren Datenverkehr zu analysieren. Durch Klicken auf \"Alle akzeptieren\" stimmen Sie der Verwendung von <PERSON> zu.", "acceptAll": "Alle akzeptieren", "decline": "<PERSON><PERSON><PERSON><PERSON>", "customize": "<PERSON><PERSON><PERSON>", "necessary": "Notwendige Cookies", "necessaryDesc": "Diese Cookies sind für das ordnungsgemäße Funktionieren der Website unerlässlich und können nicht deaktiviert werden.", "analytics": "Analyse-Cookies", "analyticsDesc": "Diese Cookies helfen uns zu verstehen, wie Besucher mit unserer Website interagieren, und helfen uns, unsere Dienste zu verbessern.", "marketing": "Marketing-Cookies", "marketingDesc": "Diese Cookies werden verwendet, um Besucher über Websites hinweg zu verfolgen, um relevante Werbung anzuzeigen.", "functional": "Funktionale Cookies", "functionalDesc": "Diese Cookies ermöglichen erweiterte Funktionen und Personalisierung auf unserer Website.", "save": "Einstellungen speichern", "settings": "Cookie-Einstellungen", "close": "Schließen", "cookiePolicy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privacyPolicy": "Datenschutzrichtlinie"}, "heroParallax": {"title": "Das ultimative Entwicklungsstudio", "subtitle": "Wir erstellen schöne Produkte mit den neuesten Technologien und Frameworks. Wir sind ein Team aus leidenschaftlichen Entwicklern und Designern, die es lieben, erstaunliche Produkte zu bauen.", "products": {"mobileApp": "Mobile App-Entwicklung", "webDev": "Web-Entwicklung", "uiux": "UI/UX-Design", "ecommerce": "E-Commerce-Lösungen", "ai": "KI-Integration", "cloud": "Cloud-Lösungen", "devops": "DevOps", "dataAnalytics": "Datenanalyse", "blockchain": "Blockchain-Entwicklung", "arvr": "AR/VR-Lösungen", "customSoftware": "Individuelle Software", "mobileGame": "Mobile Game-Entwicklung", "iot": "IoT-Lösungen", "api": "API-Entwicklung", "cybersecurity": "Cybersicherheit"}}, "legalServices": {"familyLaw": {"title": "Familienrecht", "description": "Professionelle rechtliche Unterstützung für Familien in komplexen emotionalen Situationen", "divorce": {"title": "Scheidung", "description": "Einfühlsame Begleitung durch den Scheidungsprozess"}, "maintenance": {"title": "Unterhalt", "description": "Sicherstellung einer fairen finanziellen Unterstützung für Familienmitglieder"}, "custody": {"title": "Sorgerecht & Umgang", "description": "Schutz des Kindeswohls bei familiären Auseinandersetzungen"}}, "employmentLaw": {"title": "Arbeitsrecht", "description": "Verteidigung von Arbeitnehmerrechten und Unterstützung bei der Einhaltung von Vorschriften", "protection": {"title": "Kündigungsschutz", "description": "Schutz vor ungerechtfertigter Kündigung und Belästigung am Arbeitsplatz"}, "contract": {"title": "Arbeitsverträge", "description": "Erstellung und Überprüfung fairer Arbeitsvereinbarungen"}, "warning": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Sachgerechter Umgang mit arbeitsrechtlichen Disziplinarverfahren"}}, "contractLaw": {"title": "Vertragsrecht", "description": "Professionelle Vertragserstellung und Konfliktlösung für alle Vertragsarten", "purchase": {"title": "Kaufverträge", "description": "Sichere und faire Verträge für Immobilien- und Vermögenskäufe"}, "service": {"title": "Dienstleistungsverträge", "description": "Klare und schützende Servicevereinbarungen für Unternehmen"}, "work": {"title": "Werkverträge", "description": "Umfassende Arbeitsbedingungen und Konditionen"}}, "criminalLaw": {"title": "Strafrecht", "description": "Starke Verteidigung und Vertretung in Strafverfahren", "defense": {"title": "Strafverteidigung", "description": "Energische Verteidigung gegen strafrechtliche Vorwürfe"}, "victimProtection": {"title": "Opferschutz", "description": "Unterstützung und Vertretung von Kriminalitätsopfern"}, "juvenile": {"title": "Jugendstrafrecht", "description": "Spezialisierter Ansatz für junge Straftäter"}}, "civilLaw": {"title": "Zivilrecht", "description": "<PERSON><PERSON><PERSON><PERSON> von Streitigkeiten zwischen Privatpersonen und Organisationen", "damages": {"title": "Schadensersatzansprüche", "description": "Durch<PERSON><PERSON><PERSON> von Schadensersatz für Personenschäden oder Eigentum"}, "disputes": {"title": "Nachbarschaftsstreitigkeiten", "description": "Mediation und Lösung von Konflikten im Zusammenhang mit Eigentum"}, "general": {"title": "Allgemeine Zivilsachen", "description": "Vertretung in verschiedenen zivilrechtlichen Angelegenheiten"}}, "trafficLaw": {"title": "Verkehrsrecht", "description": "Professionelle Hilfe bei allen verkehrsrechtlichen Angelegenheiten", "accident": {"title": "Verkehrsunfälle", "description": "Rechtliche Unterstützung nach Fahrzeugkollisionen und -unfällen"}, "fines": {"title": "Bußgeldsachen", "description": "Anfechten und Bearbeiten von Verkehrsverstößen und -strafen"}, "license": {"title": "Führerscheinentzug", "description": "Verteidigung gegen und Berufung gegen Führerscheinentzug"}}, "inheritanceLaw": {"title": "Erbrecht", "description": "Planung und Lösung von Erbschaftsangelegenheiten mit Sensibilität", "will": {"title": "Testament & Erbvertrag", "description": "Erstellung rechtlich fundierter Testamente zum Schutz Ihres Erbes"}, "inheritance": {"title": "Erbstreitigkeiten", "description": "<PERSON><PERSON><PERSON><PERSON> von Konflikten zwischen Erben und Begünstigten"}, "estate": {"title": "Nachlassplanung", "description": "Umfassende Planung der Vermögensverteilung"}}}, "booking": {"title": "<PERSON><PERSON><PERSON> buchen", "subtitle": "Vereinbaren Sie einen Termin mit unserem Team", "description": "Wählen Sie die Art des Termins und die gewünschte Dauer.", "bookingButton": "Buchung anfragen", "successMessage": "Buchungsanfrage gesendet!", "backToCalendar": "<PERSON><PERSON><PERSON> Termin buchen", "nameLabel": "Ihr Name", "emailLabel": "E-Mail-Adresse", "phoneLabel": "Telefonnummer", "messageLabel": "Zusätzliche Informationen", "bookingTypes": {"consultation": "Erstberatung", "meeting": "Projektbesprechung", "support": "Support-Sit<PERSON>ng"}, "durations": {"short": "15 Minuten", "medium": "30 Minuten", "long": "60 Minuten"}, "calendarButton": "Da<PERSON> ausw<PERSON>en", "submitButton": "Buchungsanfrage absenden", "backButton": "Zurück", "timeSlots": "Verfügbare Zeiten anzeigen", "contactInfo": "<PERSON><PERSON>e <PERSON>en", "selectDate": "Da<PERSON> ausw<PERSON>en", "selectTime": "Uhrzeit auswählen", "yourInformation": "<PERSON><PERSON>e <PERSON>en", "requestingBooking": "<PERSON>e beantragen eine Buchung für", "pleaseSelectDate": "<PERSON>te wählen Si<PERSON> ein Datum aus, um fortzufahren"}, "cta": {"badge": "Template Verfügbar", "title": "Diese Vorlage ist für vollständige Anpassung verfügbar", "subtitle": "Verwandeln Sie Ihre Anwaltspraxis mit einer professionellen, modernen Website, die auf Ihre Bedürfnisse zugeschnitten ist", "features": {"seo": "SEO-optimiert", "languages": "5-<PERSON><PERSON><PERSON><PERSON>Unterstützung (EN, DE, TR, RU, AR)", "themes": "Dunkles/helles Theme", "integrations": "Notion & andere Integrationen", "quality": "Premium Qualität", "delivery": "Lieferung in 7–10 Werktagen", "support": "1-zu-1 Entwickler-Support"}, "buttons": {"templates": "Alle Templates an<PERSON><PERSON>", "consultation": "Kostenlose Beratung buchen"}, "trust": {"secure": "100% Sicher", "support": "24/7 Support", "guarantee": "Geld-zurück-Garantie"}}}